# Local Development Environment Variables
# Copy this file to .env.local and fill in the actual values

# Database Configuration (Local Development)
POSTGRES_USER=cherish_user
POSTGRES_PASSWORD=cherish123
POSTGRES_DB=cherish

# JWT Configuration (Local Development - Use a simple key for testing)
JWT_SECRET_KEY=YourLocalDevelopmentSecretKeyHere123456789

# Google OAuth Configuration (Optional for local testing)
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# Frontend Configuration (Local)
NEXT_PUBLIC_API_URL=http://localhost/api
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_oauth_client_id

# Redis Configuration (Local)
REDIS_PASSWORD=redis123

# Application Configuration (Local)
APP_NAME=Cherish
APP_VERSION=1.0.0
APP_ENVIRONMENT=development
APP_DEBUG=true

# Security Configuration (Relaxed for local)
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost,http://127.0.0.1,http://localhost:3000

# Rate Limiting (Relaxed for local testing)
RATE_LIMIT_REQUESTS_PER_MINUTE=1000
RATE_LIMIT_REQUESTS_PER_HOUR=10000

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Cache Configuration
CACHE_TTL_SECONDS=300
CACHE_MAX_SIZE_MB=50

# Local Development Specific
ENABLE_SWAGGER=true
ENABLE_DETAILED_ERRORS=true
LOG_LEVEL=Debug

# Database Admin (PgAdmin)
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin123
