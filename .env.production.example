# Production Environment Variables
# Copy this file to .env.production and fill in the actual values

# Database Configuration
POSTGRES_USER=cherish_user
POSTGRES_PASSWORD=your_secure_database_password_here
POSTGRES_DB=cherish

# JWT Configuration
JWT_SECRET_KEY=your_super_secure_jwt_secret_key_at_least_32_characters_long

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# Frontend Configuration
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_oauth_client_id

# Redis Configuration (optional)
REDIS_PASSWORD=your_redis_password

# Monitoring Configuration (optional)
GRAFANA_PASSWORD=your_grafana_admin_password

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Application Configuration
APP_NAME=Cherish
APP_VERSION=1.0.0
APP_ENVIRONMENT=production
APP_DEBUG=false

# Security Configuration
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_REQUESTS_PER_HOUR=1000

# File Upload Configuration
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE_MB=100
