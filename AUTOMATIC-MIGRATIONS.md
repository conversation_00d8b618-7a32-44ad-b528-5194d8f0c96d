# Automatic Database Migrations

This document explains how the Cherish application now automatically runs database migrations when creating Docker images and containers.

## Overview

The Cherish application has been configured to automatically run Entity Framework migrations during container startup. This eliminates the need to manually run migration commands after deploying the application.

## How It Works

### 1. Entrypoint Script (`backend/entrypoint.sh`)

The backend container now uses a custom entrypoint script that:

1. **Waits for Database**: Uses `pg_isready` to ensure PostgreSQL is available
2. **Runs Migrations**: Executes `dotnet ef database update` automatically
3. **Starts Application**: Launches the main Cherish API application

### 2. Enhanced Dockerfile (`backend/Dockerfile`)

The Dockerfile has been modified to:

- Install PostgreSQL client tools for database connectivity checks
- Include .NET SDK and Entity Framework tools in the final image
- Copy the entrypoint script and make it executable
- Set environment variables for database connection parsing

### 3. Updated Docker Compose Configuration

The `docker-compose.local.yml` file now:

- Uses the new entrypoint script automatically
- Includes additional environment variables for database connection
- Increases the health check start period to allow time for migrations
- Maintains proper dependency order (database → API → frontend)

## Benefits

✅ **Automatic Setup**: No manual migration commands needed  
✅ **Consistent Deployment**: Migrations run every time containers start  
✅ **Error Handling**: Graceful handling of migration failures  
✅ **Development Friendly**: Works seamlessly with local development  
✅ **Production Ready**: Suitable for production deployments  

## Usage

### Starting the Application

Simply use the standard Docker Compose commands:

```powershell
# Start all services (migrations run automatically)
docker-compose -f docker-compose.local.yml --env-file .env.local up -d

# View logs to see migration progress
docker-compose -f docker-compose.local.yml logs api
```

### Migration Process Logs

When the API container starts, you'll see logs like:

```
🚀 Starting Cherish API container...
⏳ Waiting for database to be ready...
✅ Database is ready!
🔄 Running database migrations...
✅ Database migrations completed successfully!
🎯 Starting Cherish API application...
```

### Manual Migration (If Needed)

While migrations run automatically, you can still run them manually:

```powershell
# Run migrations manually
docker-compose -f docker-compose.local.yml exec api dotnet ef database update

# Create new migration
docker-compose -f docker-compose.local.yml exec api dotnet ef migrations add YourMigrationName
```

## Environment Variables

The following environment variables control the migration process:

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `postgres` | Database host name |
| `DB_PORT` | `5432` | Database port |
| `DB_NAME` | `cherish` | Database name |
| `DB_USER` | `cherish_user` | Database user |

## Troubleshooting

### Migration Failures

If migrations fail, the container will:
1. Log the error
2. Continue starting the application
3. Allow manual intervention

### Database Connection Issues

If the database is not ready:
1. The script waits indefinitely with 2-second intervals
2. Check database container health
3. Verify connection string configuration

### Viewing Migration Status

```powershell
# Check current migration status
docker-compose -f docker-compose.local.yml exec api dotnet ef migrations list

# View detailed logs
docker-compose -f docker-compose.local.yml logs -f api
```

## Development Workflow

### Adding New Migrations

1. Make changes to your Entity Framework models
2. Create a new migration:
   ```powershell
   docker-compose -f docker-compose.local.yml exec api dotnet ef migrations add YourMigrationName
   ```
3. Restart the container to apply the migration automatically:
   ```powershell
   docker-compose -f docker-compose.local.yml restart api
   ```

### Rolling Back Migrations

```powershell
# Rollback to specific migration
docker-compose -f docker-compose.local.yml exec api dotnet ef database update PreviousMigrationName

# Rollback all migrations
docker-compose -f docker-compose.local.yml exec api dotnet ef database update 0
```

## Production Considerations

- **Backup Strategy**: Always backup your database before deploying
- **Migration Testing**: Test migrations in staging environment first
- **Rollback Plan**: Have a rollback strategy for failed migrations
- **Monitoring**: Monitor migration logs during deployment

## Files Modified

- `backend/Dockerfile` - Enhanced with EF tools and entrypoint
- `backend/entrypoint.sh` - New script for automatic migrations
- `docker-compose.local.yml` - Updated configuration
- `deploy.ps1` - Removed manual migration step
- `deploy.sh` - Removed manual migration step
- `WINDOWS-LOCAL-TESTING.md` - Updated documentation

## Migration from Manual Process

If you were previously running migrations manually:

1. **Remove manual migration commands** from your deployment scripts
2. **Update your CI/CD pipelines** to remove migration steps
3. **Test the automatic process** in development first
4. **Monitor the first production deployment** carefully

The automatic migration system is backward compatible and will work with existing migration files.
