# Cherish Application Deployment Guide

This guide provides comprehensive instructions for deploying the Cherish application in production environments.

## 🚀 Quick Start (Docker Compose)

### Prerequisites
- Docker 20.10+ and Docker Compose 2.0+
- Domain name with DNS configured (for production)
- SSL certificates (or use self-signed for testing)

### 1. Environment Setup
```bash
# Copy and configure environment variables
cp .env.production.example .env.production
# Edit .env.production with your actual values
```

### 2. Deploy
```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy with backup and cleanup
./deploy.sh --backup --cleanup
```

### 3. Access Your Application
- **Frontend**: https://yourdomain.com
- **API**: https://yourdomain.com/api
- **Health Check**: https://yourdomain.com/health
- **API Documentation**: https://yourdomain.com/swagger

## 📋 Deployment Options

### Option 1: Docker Compose (Recommended for Start)
**Best for**: Small to medium deployments, development/staging

**Pros:**
- Simple setup and management
- All services in one configuration
- Easy to backup and restore
- Cost-effective

**Cons:**
- Single point of failure
- Limited scalability
- Manual scaling required

### Option 2: Cloud Platform Deployment

#### Azure Deployment
```bash
# Install Azure CLI
az login

# Create resource group
az group create --name cherish-rg --location eastus

# Deploy using Azure Container Instances
az container create \
  --resource-group cherish-rg \
  --name cherish-api \
  --image your-registry/cherish-api:latest \
  --dns-name-label cherish-api \
  --ports 80

# Deploy frontend to Azure Static Web Apps
az staticwebapp create \
  --name cherish-frontend \
  --resource-group cherish-rg \
  --source https://github.com/yourusername/cherish \
  --location eastus
```

#### AWS Deployment
```bash
# Install AWS CLI and configure
aws configure

# Create ECS cluster
aws ecs create-cluster --cluster-name cherish-cluster

# Deploy using AWS Copilot
copilot app init cherish
copilot env init --name production
copilot svc init --name api
copilot svc init --name frontend
copilot env deploy --name production
copilot svc deploy --name api --env production
copilot svc deploy --name frontend --env production
```

#### Google Cloud Deployment
```bash
# Install gcloud CLI
gcloud auth login

# Deploy to Cloud Run
gcloud run deploy cherish-api \
  --image gcr.io/your-project/cherish-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated

# Deploy frontend to Firebase
firebase init hosting
firebase deploy
```

### Option 3: Kubernetes Deployment
**Best for**: Large scale, enterprise environments

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/api.yaml
kubectl apply -f k8s/frontend.yaml
kubectl apply -f k8s/ingress.yaml
```

## 🔧 Configuration

### Environment Variables
Key environment variables to configure:

```bash
# Database
POSTGRES_USER=cherish_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=cherish

# JWT Security
JWT_SECRET_KEY=your_32_character_secret_key

# OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Frontend
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
```

### SSL Certificates
For production, obtain SSL certificates from:
- Let's Encrypt (free)
- CloudFlare (free with proxy)
- Commercial CA (paid)

Replace self-signed certificates in `nginx/ssl/`:
```bash
# Copy your certificates
cp your-cert.pem nginx/ssl/cert.pem
cp your-private-key.key nginx/ssl/private.key
```

### Database Configuration
- **Development**: SQLite (default)
- **Production**: PostgreSQL (recommended)
- **Cloud**: Managed database services

## 📊 Monitoring and Maintenance

### Health Checks
- **API Health**: `GET /health`
- **Database**: Connection test
- **Frontend**: Response test

### Monitoring Stack (Optional)
- **Prometheus**: Metrics collection
- **Grafana**: Visualization
- **Logs**: Centralized logging

### Backup Strategy
```bash
# Automated daily backups
./deploy.sh --backup

# Manual backup
docker-compose exec postgres pg_dump -U $POSTGRES_USER cherish > backup.sql

# Restore from backup
docker-compose exec -T postgres psql -U $POSTGRES_USER cherish < backup.sql
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Use strong, unique passwords
- [ ] Enable HTTPS with valid SSL certificates
- [ ] Configure firewall rules
- [ ] Set up rate limiting
- [ ] Enable security headers
- [ ] Regular security updates
- [ ] Database encryption at rest
- [ ] Secure secret management
- [ ] Regular backups
- [ ] Monitor access logs

### Network Security
```bash
# Configure firewall (example for Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

## 🚨 Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check logs
docker-compose logs api
docker-compose logs frontend
docker-compose logs postgres

# Check service status
docker-compose ps
```

#### Database Connection Issues
```bash
# Test database connection
docker-compose exec postgres psql -U $POSTGRES_USER -d cherish -c "SELECT 1;"

# Check database logs
docker-compose logs postgres
```

#### SSL Certificate Issues
```bash
# Verify certificate
openssl x509 -in nginx/ssl/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect yourdomain.com:443
```

### Performance Optimization
- Enable Redis caching
- Configure CDN for static assets
- Optimize database queries
- Use connection pooling
- Enable gzip compression

## 📈 Scaling

### Horizontal Scaling
- Load balancer configuration
- Multiple API instances
- Database read replicas
- CDN for static content

### Vertical Scaling
- Increase container resources
- Database performance tuning
- Memory optimization

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to server
        run: |
          ssh user@server 'cd /app && git pull && ./deploy.sh'
```

## 📞 Support

For deployment issues:
1. Check logs: `docker-compose logs`
2. Verify configuration: `.env.production`
3. Test connectivity: `curl -k https://localhost/health`
4. Review documentation
5. Contact support team

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [ASP.NET Core Deployment](https://docs.microsoft.com/en-us/aspnet/core/host-and-deploy/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Nginx Configuration](https://nginx.org/en/docs/)
