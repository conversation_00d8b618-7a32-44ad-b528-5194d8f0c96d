# Cherish Development Startup Scripts

This document explains how to start both the frontend and backend of the Cherish application using the provided startup scripts.

## Prerequisites

Before running any startup script, ensure you have the following installed:

- **.NET 8 SDK** - Download from [https://dotnet.microsoft.com/download](https://dotnet.microsoft.com/download)
- **Node.js (v18+)** - Download from [https://nodejs.org/](https://nodejs.org/)
- **PostgreSQL** (for production) or the scripts will use SQLite for development

## Available Startup Options

### Option 1: PowerShell Script (Recommended for Windows)

```powershell
.\start-dev.ps1
```

**Features:**
- Comprehensive prerequisite checking
- Automatic dependency installation/restoration
- Concurrent execution with job monitoring
- Colored output for better readability
- Graceful shutdown handling

### Option 2: Batch File (Windows Command Prompt)

```cmd
start-dev.bat
```

**Features:**
- Simple Windows batch script
- Opens backend and frontend in separate command windows
- Basic prerequisite checking
- Easy to understand and modify

### Option 3: Node.js Script (Cross-platform)

```bash
node start-dev.js
```

**Features:**
- Cross-platform compatibility (Windows, macOS, Linux)
- Colored console output
- Real-time output from both services
- Graceful shutdown with Ctrl+C

### Option 4: NPM Scripts (After installing concurrently)

First, install the root dependencies:
```bash
npm install
```

Then run:
```bash
npm run dev
```

**Available NPM Scripts:**
- `npm run dev` - Start backend first, then frontend (sequential)
- `npm run dev:sequential` - Same as above (explicit)
- `npm run dev:concurrent` - Start both services simultaneously (old behavior)
- `npm run dev:frontend` - Start only frontend
- `npm run dev:backend` - Start only backend
- `npm run build` - Build both projects
- `npm run install:all` - Install all dependencies
- `npm run clean` - Clean build artifacts

## Service URLs

When started successfully, the services will be available at:

- **Backend API**: http://localhost:5014
  - Swagger UI: http://localhost:5014/swagger
  - Health Check: http://localhost:5014/health

- **Frontend**: http://localhost:3000

## What the Scripts Do

1. **Check Prerequisites**: Verify that .NET SDK and Node.js are installed
2. **Verify Directories**: Ensure backend and frontend directories exist
3. **Install Dependencies**:
   - Install npm packages for frontend (if node_modules doesn't exist)
   - Restore NuGet packages for backend
4. **Start Backend First**: Launch the .NET API and wait for it to be ready
5. **Health Check**: Verify backend is responding at http://localhost:5014/health
6. **Start Frontend**: Once backend is ready, launch the Next.js frontend
7. **Monitor**: Display output from both services with prefixes

## Sequential Startup Process

All scripts now start the backend first and wait for it to be ready before starting the frontend. This ensures:

- ✅ Backend API is available when frontend starts
- ✅ No connection errors during frontend initialization
- ✅ Proper dependency order
- ✅ Health check verification before proceeding

The scripts will wait up to 60 seconds for the backend to respond to health checks.

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Backend (5014): Stop any existing .NET applications or change port in `backend/Cherish.Api/Properties/launchSettings.json`
   - Frontend (3000): Stop any existing Node.js applications or Next.js will automatically suggest an alternative port

2. **Database Connection Issues**
   - The backend is configured to use PostgreSQL by default
   - For development, you can switch to SQLite by changing the `Database:Provider` setting in `appsettings.json`

3. **Permission Issues (PowerShell)**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

4. **Missing Dependencies**
   - Run `npm run install:all` to install all dependencies manually
   - Or run the individual install commands:
     ```bash
     cd frontend && npm install
     cd ../backend && dotnet restore
     ```

### Manual Startup

If the scripts don't work, you can start the services manually:

**Backend:**
```bash
cd backend/Cherish.Api
dotnet run
```

**Frontend:**
```bash
cd frontend
npm run dev
```

## Development Workflow

1. **First Time Setup**: Use any of the startup scripts - they will automatically install dependencies
2. **Daily Development**: Use `.\start-dev.ps1` or `npm run dev` for the best experience
3. **Frontend Only**: Use `npm run dev:frontend` if you only need to work on the UI
4. **Backend Only**: Use `npm run dev:backend` if you only need to work on the API

## Stopping Services

- **PowerShell/Node.js scripts**: Press `Ctrl+C` to stop both services
- **Batch file**: Close the individual command windows or press `Ctrl+C` in each
- **NPM scripts**: Press `Ctrl+C` to stop both services

## Configuration

### Backend Configuration
- Port: Configured in `backend/Cherish.Api/Properties/launchSettings.json`
- Database: Configured in `backend/Cherish.Api/appsettings.json`

### Frontend Configuration
- Port: Default Next.js port (3000), can be changed with `PORT` environment variable
- API Proxy: Configured in `frontend/next.config.js` to proxy `/api/*` to backend

## Environment Variables

You may want to create a `.env.local` file in the frontend directory for local development:

```env
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

And ensure your backend `appsettings.json` has the correct database connection string and JWT settings.
