# 🪟 Windows Local Testing with Docker Compose

This guide will help you test your Cherish application locally on Windows using Docker Compose.

## 📋 Prerequisites

### 1. Install Docker Desktop for Windows
1. Download from: https://www.docker.com/products/docker-desktop/
2. Install and restart your computer
3. Start Docker Desktop
4. Verify installation:
   ```powershell
   docker --version
   docker-compose --version
   ```

### 2. Enable WSL 2 (Recommended)
1. Open PowerShell as Administrator
2. Run: `wsl --install`
3. Restart your computer
4. In Docker Desktop settings, enable "Use WSL 2 based engine"

## 🚀 Quick Start (5 Minutes)

### Step 1: Configure Environment
```powershell
# Copy local environment template
Copy-Item .env.local.example .env.local

# Edit with your preferred editor (Notepad, VS Code, etc.)
notepad .env.local
```

**Minimum Required Changes in `.env.local`:**
```bash
# These are good defaults for local testing
POSTGRES_USER=cherish_user
POSTGRES_PASSWORD=cherish123
JWT_SECRET_KEY=YourLocalDevelopmentSecretKeyHere123456789
NEXT_PUBLIC_API_URL=http://localhost/api

# Optional: Add your Google OAuth credentials for testing
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
```

### Step 2: Deploy Locally
```powershell
# Option 1: Use PowerShell script (Recommended)
.\deploy.ps1 -Backup

# Option 2: Use Docker Compose directly
docker-compose -f docker-compose.local.yml --env-file .env.local up -d --build
```

### Step 3: Access Your Application
- **Frontend**: http://localhost
- **API**: http://localhost/api
- **Health Check**: http://localhost/health
- **Swagger API Docs**: http://localhost/swagger
- **Database Admin (PgAdmin)**: http://localhost:8080
  - Email: <EMAIL>
  - Password: admin123

## 🔧 Local Development Features

### What's Included in Local Setup:
- ✅ **Hot Reload**: Frontend automatically reloads on changes
- ✅ **Database Admin**: PgAdmin for database management
- ✅ **Exposed Ports**: Direct access to PostgreSQL (5433) and Redis (6379)
- ✅ **Development Mode**: Detailed error messages and logging
- ✅ **No SSL Required**: HTTP-only for simplicity
- ✅ **Relaxed Security**: CORS and rate limiting configured for development

### Development Tools Access:
```powershell
# Database (PostgreSQL)
# Host: localhost, Port: 5433
# Username: cherish_user, Password: cherish123, Database: cherish

# Redis
# Host: localhost, Port: 6379, Password: redis123

# Connect with your favorite database tools:
# - pgAdmin (included): http://localhost:8080
# - DBeaver, DataGrip, etc.
```

## 🛠️ Management Commands

### PowerShell Commands
```powershell
# Deploy/Start services
.\deploy.ps1

# Deploy with backup
.\deploy.ps1 -Backup

# Deploy with cleanup
.\deploy.ps1 -Cleanup

# Get help
.\deploy.ps1 -Help
```

### Docker Compose Commands
```powershell
# Start services
docker-compose -f docker-compose.local.yml --env-file .env.local up -d

# Stop services
docker-compose -f docker-compose.local.yml down

# View logs
docker-compose -f docker-compose.local.yml logs

# View logs for specific service
docker-compose -f docker-compose.local.yml logs api
docker-compose -f docker-compose.local.yml logs frontend

# Restart services
docker-compose -f docker-compose.local.yml restart

# Rebuild and restart
docker-compose -f docker-compose.local.yml up -d --build

# Check service status
docker-compose -f docker-compose.local.yml ps
```

### Development Workflow
```powershell
# 1. Make code changes in your editor
# 2. Frontend changes are automatically reflected (hot reload)
# 3. For backend changes, rebuild the API:
docker-compose -f docker-compose.local.yml up -d --build api

# 4. View logs to debug:
docker-compose -f docker-compose.local.yml logs -f api
```

## 🔍 Testing Your Application

### 1. Health Checks
```powershell
# Test API health
curl http://localhost/health

# Test frontend
curl http://localhost

# Test specific API endpoints
curl http://localhost/api/auth/health
```

### 2. Database Testing
1. Open PgAdmin: http://localhost:8080
2. <NAME_EMAIL> / admin123
3. Add server connection:
   - Host: postgres
   - Port: 5433
   - Username: cherish_user
   - Password: cherish123
   - Database: cherish

### 3. API Testing
1. Open Swagger UI: http://localhost/swagger
2. Test API endpoints directly
3. Use tools like Postman or curl

## 🚨 Troubleshooting

### Common Issues on Windows

#### 1. Docker Desktop Not Starting
```powershell
# Restart Docker Desktop
# Check Windows Services: Docker Desktop Service should be running
# Try running as Administrator
```

#### 2. Port Already in Use
```powershell
# Check what's using port 80
netstat -ano | findstr :80

# Kill process if needed (replace PID)
taskkill /PID <PID> /F

# Or change ports in docker-compose.local.yml
```

#### 3. WSL 2 Issues
```powershell
# Update WSL
wsl --update

# Restart WSL
wsl --shutdown
# Then restart Docker Desktop
```

#### 4. File Sharing Issues
1. In Docker Desktop settings
2. Go to "Resources" > "File Sharing"
3. Add your project directory
4. Apply & Restart

#### 5. Services Won't Start
```powershell
# Check logs
docker-compose -f docker-compose.local.yml logs

# Check Docker Desktop logs
# Docker Desktop > Troubleshoot > View logs

# Restart everything
docker-compose -f docker-compose.local.yml down
docker system prune -f
.\deploy.ps1
```

### Performance Tips for Windows

#### 1. Use WSL 2 Backend
- Significantly faster than Hyper-V
- Better file system performance

#### 2. Allocate More Resources
1. Docker Desktop > Settings > Resources
2. Increase Memory to 4GB+
3. Increase CPU cores

#### 3. Exclude from Windows Defender
1. Add Docker Desktop installation folder
2. Add your project folder
3. Add WSL 2 directories

## 🔄 Development Workflow

### Typical Development Session
```powershell
# 1. Start your development environment
.\deploy.ps1

# 2. Open your code editor
code .  # VS Code
# or your preferred editor

# 3. Make changes to frontend (auto-reload)
# Edit files in frontend/src/

# 4. Make changes to backend (manual rebuild)
# Edit files in backend/
docker-compose -f docker-compose.local.yml up -d --build api

# 5. Test your changes
# Frontend: http://localhost
# API: http://localhost/swagger

# 6. Check logs if needed
docker-compose -f docker-compose.local.yml logs -f

# 7. Stop when done
docker-compose -f docker-compose.local.yml down
```

### Database Development
```powershell
# Access database directly
docker-compose -f docker-compose.local.yml exec postgres psql -U cherish_user -d cherish

# Run migrations manually (migrations run automatically on container startup)
docker-compose -f docker-compose.local.yml exec api dotnet ef database update

# Create new migration
docker-compose -f docker-compose.local.yml exec api dotnet ef migrations add YourMigrationName

# Note: Database migrations now run automatically when the API container starts
# You only need to run migrations manually if you want to apply them outside of container startup
```

## 📊 Monitoring Local Development

### View Real-time Logs
```powershell
# All services
docker-compose -f docker-compose.local.yml logs -f

# Specific service
docker-compose -f docker-compose.local.yml logs -f api
docker-compose -f docker-compose.local.yml logs -f frontend
```

### Resource Usage
```powershell
# Check container resource usage
docker stats

# Check Docker Desktop resource usage
# Docker Desktop > Dashboard
```

## 🎉 Success!

Your Cherish application is now running locally on Windows!

**Quick Access Links:**
- 🌐 **Application**: http://localhost
- 📚 **API Docs**: http://localhost/swagger
- 🗄️ **Database Admin**: http://localhost:8080
- ❤️ **Health Check**: http://localhost/health

**Next Steps:**
1. Test all application features
2. Make your code changes
3. Use the development workflow above
4. When ready, deploy to production using the production Docker Compose setup
