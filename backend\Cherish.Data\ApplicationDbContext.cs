using Cherish.Core.Entities;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace Cherish.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<RefreshToken> RefreshTokens => Set<RefreshToken>();
    public DbSet<Permission> Permissions => Set<Permission>();
    public DbSet<Tenant> Tenants => Set<Tenant>();
    public DbSet<Reward> Rewards => Set<Reward>();
    public DbSet<RewardTransaction> RewardTransactions => Set<RewardTransaction>();
    public DbSet<UserPoints> UserPoints => Set<UserPoints>();
    public DbSet<RewardRedemption> RewardRedemptions => Set<RewardRedemption>();
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // Configure RefreshToken
        builder.Entity<RefreshToken>(entity =>
        {
            entity.HasKey(rt => rt.Token);
            entity.Property(rt => rt.UserId).IsRequired();
            entity.Property(rt => rt.ExpiryDate).IsRequired();
            entity.Property(rt => rt.CreatedAt).IsRequired();
        });

        // Configure ApplicationUser
        builder.Entity<ApplicationUser>(entity =>
        {
            entity.Property(u => u.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(u => u.LastName).IsRequired().HasMaxLength(100);
            entity.Property(u => u.ProfilePictureUrl).HasMaxLength(2000);
            entity.Property(u => u.CreatedAt).IsRequired();

            // Global query filter for soft delete
            entity.HasQueryFilter(u => !u.IsDeleted);
        });

        // Configure ApplicationRole
        builder.Entity<ApplicationRole>(entity =>
        {
            entity.Property(r => r.Description).HasMaxLength(500);
            entity.Property(r => r.CreatedAt).IsRequired();

            // Global query filter for soft delete
            entity.HasQueryFilter(r => !r.IsDeleted);
        });

        // Configure Permission
        builder.Entity<Permission>(entity =>
        {
            entity.HasKey(p => p.Id);
            entity.Property(p => p.Name).IsRequired().HasMaxLength(100);
            entity.Property(p => p.NormalizedName).IsRequired().HasMaxLength(100);
            entity.Property(p => p.Description).HasMaxLength(500);
            entity.Property(p => p.Category).IsRequired().HasMaxLength(50);
            entity.Property(p => p.ClaimValue).IsRequired().HasMaxLength(150);
            entity.Property(p => p.CreatedAt).IsRequired();

            // Add unique constraint for normalized name
            entity.HasIndex(p => p.NormalizedName).IsUnique();

            // Global query filter for soft delete
            entity.HasQueryFilter(p => !p.IsDeleted);
        });

        // Configure Tenant
        builder.Entity<Tenant>(entity =>
        {
            entity.HasKey(t => t.Id);
            entity.Property(t => t.Name).IsRequired().HasMaxLength(100);
            entity.Property(t => t.Domain).IsRequired().HasMaxLength(100);
            entity.Property(t => t.Description).HasMaxLength(500);
            entity.Property(t => t.CreatedAt).IsRequired();

            // Add unique constraint for domain
            entity.HasIndex(t => t.Domain).IsUnique();

            // Global query filter for soft delete and active tenants
            entity.HasQueryFilter(t => !t.IsDeleted && t.IsActive);
        });

        // Configure ApplicationUser relationship with Tenant
        builder.Entity<ApplicationUser>(entity =>
        {
            // Existing user configurations...

            // Configure tenant relationship
            entity.HasOne(u => u.Tenant)
                .WithMany(t => t.Users)
                .HasForeignKey(u => u.TenantId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Configure Reward
        builder.Entity<Reward>(entity =>
        {
            entity.HasKey(r => r.Id);
            entity.Property(r => r.Title).IsRequired().HasMaxLength(200);
            entity.Property(r => r.Description).HasMaxLength(1000);
            entity.Property(r => r.CreatedAt).IsRequired();

            // Tenant relationship
            entity.HasOne(r => r.Tenant)
                .WithMany()
                .HasForeignKey(r => r.TenantId)
                .OnDelete(DeleteBehavior.Cascade);

            // Created by user relationship
            entity.HasOne(r => r.CreatedBy)
                .WithMany()
                .HasForeignKey(r => r.CreatedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Global query filter for soft delete
            entity.HasQueryFilter(r => !r.IsDeleted);
        });

        // Configure RewardTransaction
        builder.Entity<RewardTransaction>(entity =>
        {
            entity.HasKey(rt => rt.Id);
            entity.Property(rt => rt.Comment).HasMaxLength(500);
            entity.Property(rt => rt.CreatedAt).IsRequired();

            // Reward relationship
            entity.HasOne(rt => rt.Reward)
                .WithMany(r => r.Transactions)
                .HasForeignKey(rt => rt.RewardId)
                .OnDelete(DeleteBehavior.Restrict);

            // Tenant relationship
            entity.HasOne(rt => rt.Tenant)
                .WithMany()
                .HasForeignKey(rt => rt.TenantId)
                .OnDelete(DeleteBehavior.Cascade);

            // Given by user relationship
            entity.HasOne(rt => rt.GivenBy)
                .WithMany(u => u.GivenRewards)
                .HasForeignKey(rt => rt.GivenById)
                .OnDelete(DeleteBehavior.Restrict);

            // Received by user relationship
            entity.HasOne(rt => rt.ReceivedBy)
                .WithMany(u => u.ReceivedRewards)
                .HasForeignKey(rt => rt.ReceivedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Global query filter for soft delete
            entity.HasQueryFilter(rt => !rt.IsDeleted);
        });

        // Configure UserPoints
        builder.Entity<UserPoints>(entity =>
        {
            entity.HasKey(up => up.Id);
            entity.Property(up => up.CreatedAt).IsRequired();

            // User relationship
            entity.HasOne(up => up.User)
                .WithOne(u => u.UserPoints)
                .HasForeignKey<UserPoints>(up => up.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // Tenant relationship
            entity.HasOne(up => up.Tenant)
                .WithMany()
                .HasForeignKey(up => up.TenantId)
                .OnDelete(DeleteBehavior.Cascade);

            // Global query filter for soft delete
            entity.HasQueryFilter(up => !up.IsDeleted);
        });

        // Configure RewardRedemption
        builder.Entity<RewardRedemption>(entity =>
        {
            entity.HasKey(rr => rr.Id);
            entity.Property(rr => rr.Notes).HasMaxLength(1000);
            entity.Property(rr => rr.CreatedAt).IsRequired();

            // Transaction relationship
            entity.HasOne(rr => rr.Transaction)
                .WithMany()
                .HasForeignKey(rr => rr.TransactionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Tenant relationship
            entity.HasOne(rr => rr.Tenant)
                .WithMany()
                .HasForeignKey(rr => rr.TenantId)
                .OnDelete(DeleteBehavior.Cascade);

            // Redeemed by user relationship
            entity.HasOne(rr => rr.RedeemedBy)
                .WithMany(u => u.Redemptions)
                .HasForeignKey(rr => rr.RedeemedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Processed by user relationship (optional)
            entity.HasOne(rr => rr.ProcessedBy)
                .WithMany()
                .HasForeignKey(rr => rr.ProcessedById)
                .OnDelete(DeleteBehavior.Restrict);

            // Global query filter for soft delete
            entity.HasQueryFilter(rr => !rr.IsDeleted);
        });

        // Customize table names
        builder.Entity<Tenant>().ToTable("Tenants");
        builder.Entity<ApplicationUser>().ToTable("IdentityUsers");
        builder.Entity<ApplicationRole>().ToTable("IdentityRoles");
        builder.Entity<RefreshToken>().ToTable("RefreshTokens");
        builder.Entity<Permission>().ToTable("Permissions");
        builder.Entity<Reward>().ToTable("Rewards");
        builder.Entity<RewardTransaction>().ToTable("RewardTransactions");
        builder.Entity<UserPoints>().ToTable("UserPoints");
        builder.Entity<RewardRedemption>().ToTable("RewardRedemptions");
    }

    public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is ApplicationUser ||
                       e.Entity is ApplicationRole ||
                       e.Entity is Tenant ||
                       e.Entity is Permission ||
                       e.Entity is Reward ||
                       e.Entity is RewardTransaction ||
                       e.Entity is UserPoints ||
                       e.Entity is RewardRedemption);

        foreach (var entry in entries)
        {
            if (entry.State == EntityState.Added)
            {
                if (entry.Entity is ApplicationUser user)
                {
                    user.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is ApplicationRole role)
                {
                    role.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Tenant tenant)
                {
                    tenant.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Permission permission)
                {
                    permission.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Reward reward)
                {
                    reward.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is RewardTransaction transaction)
                {
                    transaction.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is UserPoints userPoints)
                {
                    userPoints.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is RewardRedemption redemption)
                {
                    redemption.CreatedAt = DateTime.UtcNow;
                }
            }
            else if (entry.State == EntityState.Modified)
            {
                if (entry.Entity is ApplicationUser user)
                {
                    user.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is ApplicationRole role)
                {
                    role.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Tenant tenant)
                {
                    tenant.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Permission permission)
                {
                    permission.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is Reward reward)
                {
                    reward.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is RewardTransaction transaction)
                {
                    transaction.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is UserPoints userPoints)
                {
                    userPoints.UpdatedAt = DateTime.UtcNow;
                }
                else if (entry.Entity is RewardRedemption redemption)
                {
                    redemption.UpdatedAt = DateTime.UtcNow;
                }
            }
        }

        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.UpdatedAt = DateTime.UtcNow;
                    break;
            }
        }

        return base.SaveChangesAsync(cancellationToken);
    }
}
