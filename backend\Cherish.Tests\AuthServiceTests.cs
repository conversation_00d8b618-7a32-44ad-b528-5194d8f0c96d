using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Auth;
using Cherish.Data.Services;
using Cherish.Tests.Mocks;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Cherish.Tests;

public class AuthServiceTests
{
    private readonly Mock<UserManager<ApplicationUser>> _mockUserManager;
    private readonly Mock<RoleManager<ApplicationRole>> _mockRoleManager;
    private readonly Mock<ITokenService> _mockTokenService;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<Identity.Data.IdentityDbContext> _mockDbContext;

    public AuthServiceTests()
    {
        // Setup UserManager mock
        var userStoreMock = new Mock<IUserStore<ApplicationUser>>();
        var options = new Mock<IOptions<IdentityOptions>>();
        var passwordHasher = new Mock<IPasswordHasher<ApplicationUser>>();
        var userValidators = new List<IUserValidator<ApplicationUser>>();
        var passwordValidators = new List<IPasswordValidator<ApplicationUser>>();
        var keyNormalizer = new Mock<ILookupNormalizer>();
        var errors = new Mock<IdentityErrorDescriber>();
        var services = new Mock<IServiceProvider>();
        var logger = new Mock<ILogger<UserManager<ApplicationUser>>>();

        _mockUserManager = new Mock<UserManager<ApplicationUser>>(
            userStoreMock.Object, options.Object, passwordHasher.Object, userValidators,
            passwordValidators, keyNormalizer.Object, errors.Object, services.Object, logger.Object);

        // Setup RoleManager mock
        var roleStoreMock = new Mock<IRoleStore<ApplicationRole>>();
        var roleValidators = new List<IRoleValidator<ApplicationRole>>();
        var roleKeyNormalizer = new Mock<ILookupNormalizer>();
        var roleErrors = new Mock<IdentityErrorDescriber>();
        var roleLogger = new Mock<ILogger<RoleManager<ApplicationRole>>>();

        _mockRoleManager = new Mock<RoleManager<ApplicationRole>>(
            roleStoreMock.Object, roleValidators, roleKeyNormalizer.Object, roleErrors.Object, roleLogger.Object);

        // Setup TokenService mock
        _mockTokenService = new Mock<ITokenService>();

        // Setup Configuration mock
        _mockConfiguration = new Mock<IConfiguration>();

        // Setup DbContext mock
        var options2 = new DbContextOptionsBuilder<Identity.Data.IdentityDbContext>()
            .UseInMemoryDatabase(databaseName: "TestDb")
            .Options;
        _mockDbContext = new Mock<Identity.Data.IdentityDbContext>(options2);
    }

    [Fact]
    public async Task RegisterAsync_WithValidRequest_ReturnsSuccessResult()
    {
        // Arrange
        var request = new RegisterRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Password = "Password123!",
            ConfirmPassword = "Password123!"
        };

        _mockUserManager.Setup(m => m.FindByEmailAsync(request.Email))
            .ReturnsAsync((ApplicationUser?)null);

        _mockUserManager.Setup(m => m.CreateAsync(It.IsAny<ApplicationUser>(), request.Password))
            .ReturnsAsync(IdentityResult.Success);

        _mockUserManager.Setup(m => m.AddToRoleAsync(It.IsAny<ApplicationUser>(), "User"))
            .ReturnsAsync(IdentityResult.Success);

        // Setup for email verification
        _mockUserManager.Setup(m => m.GenerateEmailConfirmationTokenAsync(It.IsAny<ApplicationUser>()))
            .ReturnsAsync("verification-token");

        _mockConfiguration.Setup(c => c["App:FrontendBaseUrl"])
            .Returns("http://localhost:4000");

        // Create a default tenant for testing
        var defaultTenant = new Tenant
        {
            Id = Guid.NewGuid(),
            Name = "Default",
            IsActive = true,
            IsDeleted = false
        };

        // Create a mock tenant service
        var mockTenantService = new Mock<ITenantService>();
        mockTenantService.Setup(t => t.GetByNameAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(defaultTenant != null ? new Core.Models.Tenants.TenantDto
            {
                Id = defaultTenant.Id,
                Name = defaultTenant.Name,
                IsActive = defaultTenant.IsActive
            } : null);

        var authService = new MockAuthService(
            _mockUserManager.Object,
            _mockRoleManager.Object,
            _mockTokenService.Object,
            _mockConfiguration.Object,
            _mockDbContext.Object,
            mockTenantService.Object,
            defaultTenant);

        // Act
        var result = await authService.RegisterAsync(request);

        // Assert
        Assert.True(result.Succeeded);
        _mockUserManager.Verify(m => m.CreateAsync(It.IsAny<ApplicationUser>(), request.Password), Times.Once);
        _mockUserManager.Verify(m => m.AddToRoleAsync(It.IsAny<ApplicationUser>(), "User"), Times.Once);
        _mockUserManager.Verify(m => m.GenerateEmailConfirmationTokenAsync(It.IsAny<ApplicationUser>()), Times.Once);
    }

    [Fact]
    public async Task RegisterAsync_WithExistingEmail_ReturnsFailureResult()
    {
        // Arrange
        var request = new RegisterRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Password = "Password123!",
            ConfirmPassword = "Password123!"
        };

        var existingUser = new ApplicationUser
        {
            Email = request.Email
        };

        _mockUserManager.Setup(m => m.FindByEmailAsync(request.Email))
            .ReturnsAsync(existingUser);

        // Create a mock tenant service
        var mockTenantService = new Mock<ITenantService>();
        mockTenantService.Setup(t => t.GetByNameAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Core.Models.Tenants.TenantDto?)null);

        var authService = new MockAuthService(
            _mockUserManager.Object,
            _mockRoleManager.Object,
            _mockTokenService.Object,
            _mockConfiguration.Object,
            _mockDbContext.Object,
            mockTenantService.Object);

        // Act
        var result = await authService.RegisterAsync(request);

        // Assert
        Assert.False(result.Succeeded);
        Assert.Contains("User with this email already exists", result.Errors);
        _mockUserManager.Verify(m => m.CreateAsync(It.IsAny<ApplicationUser>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public async Task LoginAsync_WithValidCredentials_ReturnsSuccessResult()
    {
        // Arrange
        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        var user = new ApplicationUser
        {
            Email = request.Email,
            IsActive = true,
            EmailConfirmed = true
        };

        _mockUserManager.Setup(m => m.FindByEmailAsync(request.Email))
            .ReturnsAsync(user);

        _mockUserManager.Setup(m => m.CheckPasswordAsync(user, request.Password))
            .ReturnsAsync(true);

        _mockTokenService.Setup(m => m.GenerateTokensAsync(user))
            .ReturnsAsync(new AuthResult { Succeeded = true });

        // Create a mock tenant service
        var mockTenantService = new Mock<ITenantService>();
        mockTenantService.Setup(t => t.GetByNameAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Core.Models.Tenants.TenantDto?)null);

        var authService = new MockAuthService(
            _mockUserManager.Object,
            _mockRoleManager.Object,
            _mockTokenService.Object,
            _mockConfiguration.Object,
            _mockDbContext.Object,
            mockTenantService.Object);

        // Act
        var result = await authService.LoginAsync(request);

        // Assert
        Assert.True(result.Succeeded);
        _mockUserManager.Verify(m => m.CheckPasswordAsync(user, request.Password), Times.Once);
        _mockTokenService.Verify(m => m.GenerateTokensAsync(user), Times.Once);
    }

    [Fact]
    public async Task LoginAsync_WithInvalidCredentials_ReturnsFailureResult()
    {
        // Arrange
        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "WrongPassword"
        };

        var user = new ApplicationUser
        {
            Email = request.Email,
            IsActive = true,
            EmailConfirmed = true
        };

        _mockUserManager.Setup(m => m.FindByEmailAsync(request.Email))
            .ReturnsAsync(user);

        _mockUserManager.Setup(m => m.CheckPasswordAsync(user, request.Password))
            .ReturnsAsync(false);

        // Create a mock tenant service
        var mockTenantService = new Mock<ITenantService>();
        mockTenantService.Setup(t => t.GetByNameAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Core.Models.Tenants.TenantDto?)null);

        var authService = new MockAuthService(
            _mockUserManager.Object,
            _mockRoleManager.Object,
            _mockTokenService.Object,
            _mockConfiguration.Object,
            _mockDbContext.Object,
            mockTenantService.Object);

        // Act
        var result = await authService.LoginAsync(request);

        // Assert
        Assert.False(result.Succeeded);
        Assert.Contains("Invalid email or password", result.Errors);
        _mockUserManager.Verify(m => m.CheckPasswordAsync(user, request.Password), Times.Once);
        _mockTokenService.Verify(m => m.GenerateTokensAsync(It.IsAny<ApplicationUser>()), Times.Never);
    }

    [Fact]
    public async Task LoginAsync_WithInactiveUser_ReturnsFailureResult()
    {
        // Arrange
        var request = new LoginRequest
        {
            Email = "<EMAIL>",
            Password = "Password123!"
        };

        var user = new ApplicationUser
        {
            Email = request.Email,
            IsActive = false
        };

        _mockUserManager.Setup(m => m.FindByEmailAsync(request.Email))
            .ReturnsAsync(user);

        // Create a mock tenant service
        var mockTenantService = new Mock<ITenantService>();
        mockTenantService.Setup(t => t.GetByNameAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((Core.Models.Tenants.TenantDto?)null);

        var authService = new MockAuthService(
            _mockUserManager.Object,
            _mockRoleManager.Object,
            _mockTokenService.Object,
            _mockConfiguration.Object,
            _mockDbContext.Object,
            mockTenantService.Object);

        // Act
        var result = await authService.LoginAsync(request);

        // Assert
        Assert.False(result.Succeeded);
        Assert.Contains("User account is inactive or deleted", result.Errors);
        _mockUserManager.Verify(m => m.CheckPasswordAsync(user, It.IsAny<string>()), Times.Never);
    }
}
