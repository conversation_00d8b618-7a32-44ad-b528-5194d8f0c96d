using Cherish.Core.Entities;
using Cherish.Core.Interfaces;
using Cherish.Core.Models.Auth;
using Cherish.Data.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;

namespace Cherish.Tests.Mocks;

public class MockAuthService : AuthService
{
    private readonly Tenant? _defaultTenant;

    public MockAuthService(
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        ITokenService tokenService,
        IConfiguration configuration,
        Identity.Data.IdentityDbContext dbContext,
        ITenantService tenantService,
        Tenant? defaultTenant = null)
        : base(userManager, roleManager, tokenService, configuration, dbContext, tenantService)
    {
        _defaultTenant = defaultTenant;
    }

    protected override Task<Tenant?> GetDefaultTenantAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(_defaultTenant);
    }
}
