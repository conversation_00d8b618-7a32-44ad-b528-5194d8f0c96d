# Cherish Application Deployment Script for Windows
# This script handles the complete deployment process on Windows

param(
    [switch]$Backup,
    [switch]$Cleanup,
    [switch]$Help
)

# Configuration
$ComposeFile = "docker-compose.production.yml"
$EnvFile = ".env.production"
$BackupDir = "./database/backups"
$LogFile = "./deployment.log"

# Functions
function Write-Log {
    param($Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host $logMessage -ForegroundColor Blue
    $logMessage | Out-File -FilePath $LogFile -Append
}

function Write-Error-Log {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
    "[ERROR] $Message" | Out-File -FilePath $LogFile -Append
    exit 1
}

function Write-Warning-Log {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
    "[WARNING] $Message" | Out-File -FilePath $LogFile -Append
}

function Write-Success-Log {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
    "[SUCCESS] $Message" | Out-File -FilePath $LogFile -Append
}

function Show-Help {
    Write-Host "Cherish Application Deployment Script for Windows" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\deploy.ps1 [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Backup    Create database backup before deployment"
    Write-Host "  -Cleanup   Clean up old Docker containers and images"
    Write-Host "  -Help      Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Green
    Write-Host "  .\deploy.ps1                    # Basic deployment"
    Write-Host "  .\deploy.ps1 -Backup           # Deploy with backup"
    Write-Host "  .\deploy.ps1 -Backup -Cleanup  # Deploy with backup and cleanup"
    exit 0
}

# Check prerequisites
function Test-Prerequisites {
    Write-Log "Checking prerequisites..."

    # Check if Docker is installed and running
    try {
        $dockerVersion = docker --version
        Write-Log "Docker found: $dockerVersion"
    }
    catch {
        Write-Error-Log "Docker is not installed or not in PATH. Please install Docker Desktop for Windows."
    }

    try {
        docker info | Out-Null
    }
    catch {
        Write-Error-Log "Docker is not running. Please start Docker Desktop."
    }

    # Check if Docker Compose is available
    try {
        $composeVersion = docker-compose --version
        Write-Log "Docker Compose found: $composeVersion"
    }
    catch {
        Write-Error-Log "Docker Compose is not installed. Please install Docker Desktop for Windows."
    }

    # Check if environment file exists
    if (-not (Test-Path $EnvFile)) {
        Write-Error-Log "Environment file $EnvFile not found. Please copy .env.production.example to $EnvFile and configure it."
    }

    Write-Success-Log "Prerequisites check passed"
}

# Create necessary directories
function New-Directories {
    Write-Log "Creating necessary directories..."

    $directories = @(
        "nginx/ssl",
        "nginx/logs",
        "database/init",
        "database/backups",
        "monitoring",
        $BackupDir
    )

    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }

    Write-Success-Log "Directories created"
}

# Generate SSL certificates (self-signed for development)
function New-SSLCertificates {
    Write-Log "Checking SSL certificates..."

    if (-not (Test-Path "nginx/ssl/cert.pem") -or -not (Test-Path "nginx/ssl/private.key")) {
        Write-Warning-Log "SSL certificates not found. Generating self-signed certificates..."
        Write-Warning-Log "For production, replace these with proper SSL certificates from a CA."

        # Check if OpenSSL is available
        try {
            openssl version | Out-Null
        }
        catch {
            Write-Warning-Log "OpenSSL not found. Using PowerShell to create basic certificates..."

            # Create a basic self-signed certificate using PowerShell
            $cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My"
            $certPath = "nginx/ssl/cert.pem"
            $keyPath = "nginx/ssl/private.key"

            # Export certificate
            Export-Certificate -Cert $cert -FilePath "nginx/ssl/cert.cer" | Out-Null

            # Convert to PEM format (basic conversion)
            $certContent = [System.Convert]::ToBase64String([System.IO.File]::ReadAllBytes("nginx/ssl/cert.cer"))
            $pemCert = "-----BEGIN CERTIFICATE-----`n"
            for ($i = 0; $i -lt $certContent.Length; $i += 64) {
                $pemCert += $certContent.Substring($i, [Math]::Min(64, $certContent.Length - $i)) + "`n"
            }
            $pemCert += "-----END CERTIFICATE-----"

            $pemCert | Out-File -FilePath $certPath -Encoding ASCII

            # Create a dummy private key (for development only)
            "-----BEGIN PRIVATE KEY-----`nDUMMY_KEY_FOR_DEVELOPMENT_ONLY`n-----END PRIVATE KEY-----" | Out-File -FilePath $keyPath -Encoding ASCII

            Remove-Item "nginx/ssl/cert.cer" -Force
        }

        Write-Success-Log "Self-signed SSL certificates generated"
    }
    else {
        Write-Success-Log "SSL certificates found"
    }
}

# Build and start services
function Start-Services {
    Write-Log "Building and deploying services..."

    # Pull latest images
    Write-Log "Pulling latest images..."
    docker-compose -f $ComposeFile --env-file $EnvFile pull

    # Build custom images
    Write-Log "Building custom images..."
    docker-compose -f $ComposeFile --env-file $EnvFile build --no-cache

    # Start services
    Write-Log "Starting services..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d

    Write-Success-Log "Services deployed"
}

# Wait for services to be healthy
function Wait-ForServices {
    Write-Log "Waiting for services to be healthy..."

    $maxAttempts = 30
    $attempt = 1

    while ($attempt -le $maxAttempts) {
        Write-Log "Health check attempt $attempt/$maxAttempts"

        # Check if all services are healthy
        $unhealthyServices = docker-compose -f $ComposeFile --env-file $EnvFile ps | Select-String "unhealthy"

        if ($unhealthyServices) {
            Write-Warning-Log "Some services are not healthy yet. Waiting..."
            Start-Sleep -Seconds 10
            $attempt++
        }
        else {
            Write-Success-Log "All services are healthy"
            return
        }
    }

    Write-Error-Log "Services failed to become healthy within the timeout period"
}

# Run database migrations
function Invoke-Migrations {
    Write-Log "Running database migrations..."

    # Wait for database to be ready
    Start-Sleep -Seconds 10

    # Run migrations through the API container
    try {
        docker-compose -f $ComposeFile --env-file $EnvFile exec -T api dotnet ef database update --no-build
        Write-Success-Log "Database migrations completed"
    }
    catch {
        Write-Warning-Log "Migration failed or no migrations to run"
    }
}

# Create database backup
function New-DatabaseBackup {
    if ($Backup) {
        Write-Log "Creating database backup..."

        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupFile = "$BackupDir/cherish_backup_$timestamp.sql"

        try {
            # Load environment variables
            Get-Content $EnvFile | ForEach-Object {
                if ($_ -match '^([^=]+)=(.*)$') {
                    [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
                }
            }

            $postgresUser = $env:POSTGRES_USER
            docker-compose -f $ComposeFile --env-file $EnvFile exec -T postgres pg_dump -U $postgresUser -d cherish --no-owner --no-privileges | Out-File -FilePath $backupFile -Encoding UTF8

            Write-Success-Log "Database backup created: $backupFile"
        }
        catch {
            Write-Warning-Log "Failed to create database backup: $_"
        }
    }
}

# Show deployment status
function Show-Status {
    Write-Log "Deployment Status:"
    Write-Host ""
    docker-compose -f $ComposeFile --env-file $EnvFile ps
    Write-Host ""
    Write-Log "Service URLs:"
    Write-Host "  Frontend: http://localhost" -ForegroundColor Cyan
    Write-Host "  API: http://localhost/api" -ForegroundColor Cyan
    Write-Host "  Health Check: http://localhost/health" -ForegroundColor Cyan
    Write-Host "  Swagger: http://localhost/swagger" -ForegroundColor Cyan
    Write-Host "  Grafana: http://localhost:3001 (if enabled)" -ForegroundColor Cyan
    Write-Host "  Prometheus: http://localhost:9090 (if enabled)" -ForegroundColor Cyan
    Write-Host ""
    Write-Success-Log "Deployment completed successfully!"
}

# Cleanup function
function Invoke-Cleanup {
    if ($Cleanup) {
        Write-Log "Cleaning up old containers and images..."
        docker system prune -f
        docker volume prune -f
        Write-Success-Log "Cleanup completed"
    }
}

# Main deployment process
function Main {
    if ($Help) {
        Show-Help
    }

    Write-Log "Starting Cherish application deployment on Windows..."

    # Execute deployment steps
    Test-Prerequisites
    New-Directories
    New-SSLCertificates
    New-DatabaseBackup
    Start-Services
    Wait-ForServices
    # Note: Migrations are now run automatically during container startup
    Show-Status
    Invoke-Cleanup
}

# Run main function
Main
