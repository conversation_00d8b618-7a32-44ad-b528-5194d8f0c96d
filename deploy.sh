#!/bin/bash

# Cherish Application Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="./backups"
LOG_FILE="./deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi

    if ! docker info &> /dev/null; then
        error "Docker is not running. Please start Docker first."
    fi

    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi

    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found. Please copy .env.production.example to $ENV_FILE and configure it."
    fi

    success "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."

    mkdir -p nginx/ssl
    mkdir -p nginx/logs
    mkdir -p database/init
    mkdir -p database/backups
    mkdir -p monitoring
    mkdir -p "$BACKUP_DIR"

    success "Directories created"
}

# Generate SSL certificates (self-signed for development)
generate_ssl_certificates() {
    log "Checking SSL certificates..."

    if [ ! -f "nginx/ssl/cert.pem" ] || [ ! -f "nginx/ssl/private.key" ]; then
        warning "SSL certificates not found. Generating self-signed certificates..."
        warning "For production, replace these with proper SSL certificates from a CA."

        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/private.key \
            -out nginx/ssl/cert.pem \
            -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

        success "Self-signed SSL certificates generated"
    else
        success "SSL certificates found"
    fi
}

# Build and start services
deploy_services() {
    log "Building and deploying services..."

    # Pull latest images
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull

    # Build custom images
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache

    # Start services
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d

    success "Services deployed"
}

# Wait for services to be healthy
wait_for_services() {
    log "Waiting for services to be healthy..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log "Health check attempt $attempt/$max_attempts"

        # Check if all services are healthy
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps | grep -q "unhealthy"; then
            warning "Some services are not healthy yet. Waiting..."
            sleep 10
            ((attempt++))
        else
            success "All services are healthy"
            return 0
        fi
    done

    error "Services failed to become healthy within the timeout period"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."

    # Wait for database to be ready
    sleep 10

    # Run migrations through the API container
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T api \
        dotnet ef database update --no-build || warning "Migration failed or no migrations to run"

    success "Database migrations completed"
}

# Create database backup
backup_database() {
    if [ "$1" = "--backup" ]; then
        log "Creating database backup..."

        local backup_file="$BACKUP_DIR/cherish_backup_$(date +%Y%m%d_%H%M%S).sql"

        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T postgres \
            pg_dump -U "$POSTGRES_USER" cherish > "$backup_file"

        success "Database backup created: $backup_file"
    fi
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
    echo ""
    log "Service URLs:"
    echo "  Frontend: https://localhost"
    echo "  API: https://localhost/api"
    echo "  Health Check: https://localhost/health"
    echo "  Swagger: https://localhost/swagger"
    echo "  Grafana: http://localhost:3001 (if enabled)"
    echo "  Prometheus: http://localhost:9090 (if enabled)"
    echo ""
    success "Deployment completed successfully!"
}

# Cleanup function
cleanup() {
    if [ "$1" = "--cleanup" ]; then
        log "Cleaning up old containers and images..."
        docker system prune -f
        docker volume prune -f
        success "Cleanup completed"
    fi
}

# Main deployment process
main() {
    log "Starting Cherish application deployment..."

    # Parse command line arguments
    BACKUP_FLAG=""
    CLEANUP_FLAG=""

    for arg in "$@"; do
        case $arg in
            --backup)
                BACKUP_FLAG="--backup"
                ;;
            --cleanup)
                CLEANUP_FLAG="--cleanup"
                ;;
            --help)
                echo "Usage: $0 [--backup] [--cleanup] [--help]"
                echo "  --backup   Create database backup before deployment"
                echo "  --cleanup  Clean up old Docker containers and images"
                echo "  --help     Show this help message"
                exit 0
                ;;
        esac
    done

    # Execute deployment steps
    check_prerequisites
    create_directories
    generate_ssl_certificates
    backup_database "$BACKUP_FLAG"
    deploy_services
    wait_for_services
    # Note: Migrations are now run automatically during container startup
    show_status
    cleanup "$CLEANUP_FLAG"
}

# Run main function with all arguments
main "$@"
