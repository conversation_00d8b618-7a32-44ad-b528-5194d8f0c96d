'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Button,
  Avatar,
  IconButton,
  Checkbox,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';

export default function Dashboard() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  // Redirect SuperAdmin users to their dashboard
  useEffect(() => {
    if (!isLoading && user?.roles.includes('SuperAdmin')) {
      router.push('/super-admin/dashboard');
    }
  }, [user, isLoading, router]);

  const stats = [
    {
      title: 'Recognition Points',
      value: '1,250',
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      color: 'primary.main',
    },
    {
      title: 'Recognitions Received',
      value: '24',
      icon: <TrendingUpIcon sx={{ fontSize: 40 }} />,
      color: 'success.main',
    },
    {
      title: 'Engagement Rate',
      value: '87.5%',
      icon: <AssessmentIcon sx={{ fontSize: 40 }} />,
      color: 'info.main',
    },
    {
      title: 'Rewards Redeemed',
      value: '8',
      icon: <AssignmentIcon sx={{ fontSize: 40 }} />,
      color: 'warning.main',
    },
  ];

  const recentRecognitions = [
    { name: 'John Smith', department: 'Engineering', type: 'Teamwork', date: '2023-04-15' },
    { name: 'Sarah Johnson', department: 'Marketing', type: 'Innovation', date: '2023-04-14' },
    { name: 'Michael Brown', department: 'Sales', type: 'Excellence', date: '2023-04-13' },
    { name: 'Emily Davis', department: 'Customer Support', type: 'Leadership', date: '2023-04-12' },
    { name: 'Robert Wilson', department: 'Product', type: 'Collaboration', date: '2023-04-11' },
  ];

  const availableRewards = [
    { title: 'Amazon Gift Card ($50)', points: 500, category: 'Gift Cards', expiryDate: '2023-06-30' },
    { title: 'Extra Day Off', points: 1000, category: 'Time Off', expiryDate: '2023-07-15' },
    { title: 'Company Swag Box', points: 300, category: 'Merchandise', expiryDate: '2023-06-15' },
    { title: 'Lunch with CEO', points: 750, category: 'Experiences', expiryDate: '2023-07-30' },
    { title: 'Professional Development Course', points: 600, category: 'Learning', expiryDate: '2023-08-01' },
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Teamwork': return 'success';
      case 'Innovation': return 'info';
      case 'Excellence': return 'primary';
      case 'Leadership': return 'warning';
      case 'Collaboration': return 'secondary';
      default: return 'default';
    }
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Welcome back, {user?.firstName}! Here's an overview of your recognition and rewards activity.
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        {stats.map((stat) => (
          <Grid item xs={12} sm={6} md={3} key={stat.title}>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: 'column',
                height: 140,
                position: 'relative',
                overflow: 'hidden',
              }}
              elevation={2}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: -10,
                  right: -10,
                  opacity: 0.1,
                  transform: 'rotate(15deg)',
                }}
              >
                {stat.icon}
              </Box>
              <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                {stat.title}
              </Typography>
              <Typography variant="h4" component="div" sx={{ color: stat.color }}>
                {stat.value}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} lg={7}>
          <Paper sx={{ p: 2 }} elevation={2}>
            <Typography variant="h6" gutterBottom>
              Recent Recognitions
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Department</TableCell>
                    <TableCell>Recognition Type</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentRecognitions.map((recognition) => (
                    <TableRow key={recognition.name} hover>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{ mr: 2, bgcolor: 'primary.main' }}
                          >
                            {recognition.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          {recognition.name}
                        </Box>
                      </TableCell>
                      <TableCell>{recognition.department}</TableCell>
                      <TableCell>
                        <Chip
                          label={recognition.type}
                          color={getTypeColor(recognition.type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{recognition.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button variant="contained" color="primary">
                View all recognitions
              </Button>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} lg={5}>
          <Paper sx={{ p: 2 }} elevation={2}>
            <Typography variant="h6" gutterBottom>
              Available Rewards
            </Typography>
            <Box>
              {availableRewards.map((reward, index) => (
                <Box
                  key={reward.title}
                  sx={{
                    py: 2,
                    display: 'flex',
                    alignItems: 'center',
                    borderBottom: index < availableRewards.length - 1 ? 1 : 0,
                    borderColor: 'divider',
                  }}
                >
                  <Box sx={{ ml: 2, flex: 1 }}>
                    <Typography variant="body2">{reward.title}</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      <Chip
                        label={`${reward.points} points`}
                        size="small"
                        color="primary"
                        sx={{ mr: 1 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        Expires: {reward.expiryDate}
                      </Typography>
                    </Box>
                  </Box>
                  <Button variant="outlined" size="small">
                    Redeem
                  </Button>
                </Box>
              ))}
            </Box>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Button variant="contained" color="primary">
                View all rewards
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
