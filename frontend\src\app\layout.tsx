import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import '@/styles/globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { NavigationProvider } from '@/contexts/NavigationContext';
import { GoogleOAuthProvider } from '@react-oauth/google';
import ThemeRegistry from '@/components/ThemeRegistry';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Cherish - Rewards & Recognition',
  description: 'A comprehensive employee rewards and recognition platform for organizations',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ThemeRegistry>
          <NavigationProvider>
            <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || 'your-google-client-id'}>
              <AuthProvider>
                {children}
              </AuthProvider>
            </GoogleOAuthProvider>
          </NavigationProvider>
        </ThemeRegistry>
      </body>
    </html>
  );
}
