'use client';

import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Container,
  Box,
  Grid,
  Card,
  CardContent,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Assignment as AssignmentIcon,
  BarChart as BarChartIcon,
  Facebook as FacebookIcon,
  Twitter as TwitterIcon,
  GitHub as GitHubIcon,
} from '@mui/icons-material';

export default function Home() {
  const { isAuthenticated, logout, user } = useAuth();
  const theme = useTheme();

  const features = [
    {
      name: 'Peer Recognition',
      description: 'Empower employees to recognize and appreciate each other\'s contributions in real-time.',
      icon: <PeopleIcon />
    },
    {
      name: 'Rewards Catalog',
      description: 'Offer a diverse range of rewards that employees can redeem with their earned points.',
      icon: <DashboardIcon />
    },
    {
      name: 'Achievement Tracking',
      description: 'Track milestones, work anniversaries, and achievements to celebrate employee success.',
      icon: <AssignmentIcon />
    },
    {
      name: 'Analytics & Insights',
      description: 'Gain valuable insights into recognition patterns and employee engagement metrics.',
      icon: <BarChartIcon />
    },
  ];

  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      <AppBar position="static" color="transparent" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, color: theme.palette.primary.main }}>
            Cherish
          </Typography>
          <Box>
            {isAuthenticated ? (
              <>
                {!user?.roles.includes('SuperAdmin') && (
                  <Button
                    component={Link}
                    href="/dashboard"
                    variant="outlined"
                    sx={{ mr: 2 }}
                  >
                    Dashboard
                  </Button>
                )}
                {user?.roles.includes('SuperAdmin') && (
                  <Button
                    component={Link}
                    href="/super-admin/dashboard"
                    variant="outlined"
                    sx={{ mr: 2 }}
                  >
                    Super Admin Dashboard
                  </Button>
                )}
                <Button
                  onClick={logout}
                  variant="contained"
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Button
                  component={Link}
                  href="/login"
                  variant="outlined"
                  sx={{ mr: 2 }}
                >
                  Login
                </Button>
                <Button
                  component={Link}
                  href="/register"
                  variant="contained"
                >
                  Register
                </Button>
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      <Box component="main" sx={{ flexGrow: 1 }}>
        <Container maxWidth="xl">
          <Box sx={{ py: 10 }}>
            <Grid container spacing={8} alignItems="center">
              <Grid item xs={12} lg={6}>
                <Typography
                  variant="overline"
                  component="span"
                  color="primary"
                  sx={{ fontWeight: 600 }}
                >
                  Introducing
                </Typography>
                <Typography variant="h2" component="h1" sx={{ mt: 1, fontWeight: 800 }}>
                  Recognize & Reward with{' '}
                  <Box component="span" color="primary.main">
                    Cherish
                  </Box>
                </Typography>
                <Typography variant="h6" color="text.secondary" sx={{ mt: 3 }}>
                  Cherish is a comprehensive employee recognition platform designed to boost engagement and celebrate success.
                  Create a culture of appreciation where employees feel valued and motivated.
                </Typography>
                <Box sx={{ mt: 6, display: 'flex', gap: 2 }}>
                  <Button
                    component={Link}
                    href="/register"
                    variant="contained"
                    size="large"
                  >
                    Get started
                  </Button>
                  <Button
                    component={Link}
                    href="/about"
                    variant="outlined"
                    size="large"
                  >
                    Learn more
                  </Button>
                </Box>
              </Grid>
              <Grid item xs={12} lg={6}>
                <Box
                  component="img"
                  src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80"
                  alt="Employee recognition dashboard"
                  sx={{
                    width: '100%',
                    borderRadius: 2,
                    boxShadow: 3,
                  }}
                />
              </Grid>
            </Grid>
          </Box>

          <Box sx={{ py: 10, bgcolor: 'background.paper' }}>
            <Container maxWidth="lg">
              <Typography variant="h6" component="h2" color="primary" align="center" gutterBottom>
                Features
              </Typography>
              <Typography variant="h3" align="center" gutterBottom>
                Everything you need for employee recognition
              </Typography>
              <Typography variant="h6" align="center" color="text.secondary" sx={{ mb: 8 }}>
                Cherish provides a comprehensive set of tools to help you recognize, reward, and retain your top talent.
              </Typography>

              <Grid container spacing={4}>
                {features.map((feature) => (
                  <Grid item xs={12} md={6} key={feature.name}>
                    <Card elevation={0} sx={{ height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 1,
                              bgcolor: 'primary.main',
                              color: 'primary.contrastText',
                              mr: 2,
                            }}
                          >
                            {feature.icon}
                          </Box>
                          <Box>
                            <Typography variant="h6" gutterBottom>
                              {feature.name}
                            </Typography>
                            <Typography color="text.secondary">
                              {feature.description}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Container>
          </Box>
        </Container>
      </Box>

      <Box component="footer" sx={{ bgcolor: 'grey.900', color: 'grey.50', py: 6, mt: 'auto' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Typography variant="h5" gutterBottom>
              Cherish
              </Typography>
              <Typography variant="body2" color="grey.400">
                © 2025 Cherish. All rights reserved.
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' }, gap: 2 }}>
                <IconButton color="inherit" component="a" href="#">
                  <FacebookIcon />
                </IconButton>
                <IconButton color="inherit" component="a" href="#">
                  <TwitterIcon />
                </IconButton>
                <IconButton color="inherit" component="a" href="#">
                  <GitHubIcon />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
}
