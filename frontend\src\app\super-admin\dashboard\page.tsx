'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  Stack,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Assignment as AssignmentIcon,
  BarChart as BarChartIcon,
  PersonAdd as PersonAddIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import NavigationLink from '@/components/NavigationLink';
import { getAllTenants } from '@/services/tenantService';
import { getSystemHealth } from '@/services/systemService';
import { getAggregateMetrics, getUserOverview } from '@/services/superAdminService';
import { TenantDto, TenantMetricsDto } from '@/types/tenants';

export default function SuperAdminDashboard() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [tenantsLoading, setTenantsLoading] = useState(true);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(true);
  const [aggregateMetrics, setAggregateMetrics] = useState<any>(null);
  const [userOverview, setUserOverview] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/dashboard');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const data = await getAllTenants();
        setTenants(data);
      } catch (error) {
        console.error('Error fetching tenants:', error);
      } finally {
        setTenantsLoading(false);
      }
    };

    const fetchAggregateMetrics = async () => {
      try {
        const data = await getAggregateMetrics();
        if (data) {
          setAggregateMetrics(data);
        }
      } catch (error) {
        console.error('Error fetching aggregate metrics:', error);
      } finally {
        setMetricsLoading(false);
      }
    };

    const fetchUserOverview = async () => {
      try {
        const data = await getUserOverview();
        if (data) {
          setUserOverview(data);
        }
      } catch (error) {
        console.error('Error fetching user overview:', error);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchTenants();
    fetchAggregateMetrics();
    fetchUserOverview();
  }, []);

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
        SuperAdmin Dashboard
      </Typography>
      <Typography variant="subtitle1" sx={{ color: '#d1d5db', mb: 3 }}>
        Overview of all tenants, usage metrics, and user statistics
      </Typography>

      {/* Tenants Overview Tile */}
      <Grid container spacing={3} sx={{ mt: 2, mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, bgcolor: '#374151', color: 'white', border: '1px solid #4b5563' }} elevation={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                Tenants Overview
              </Typography>
              <Button
                component={NavigationLink}
                href="/super-admin/tenants"
                variant="outlined"
                startIcon={<VisibilityIcon />}
                size="small"
                sx={{
                  borderColor: '#fbbf24',
                  color: '#fbbf24',
                  '&:hover': {
                    borderColor: '#f59e0b',
                    bgcolor: 'rgba(251, 191, 36, 0.1)',
                  },
                }}
              >
                View All Tenants
              </Button>
            </Box>

            {tenantsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: '#fbbf24', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            Total Tenants
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {tenants.length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: '#10b981', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            Active Tenants
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {tenants.filter(t => t.isActive).length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: '#ef4444', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            Inactive Tenants
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {tenants.filter(t => !t.isActive).length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
                  Recent Tenants
                </Typography>
                <List>
                  {tenants.slice(0, 5).map((tenant) => (
                    <ListItem
                      key={tenant.id}
                      component={NavigationLink}
                      href={`/super-admin/tenants/${tenant.id}`}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': { bgcolor: '#4b5563' },
                        color: 'white',
                        textDecoration: 'none',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <ListItemIcon>
                        <BusinessIcon sx={{ color: tenant.isActive ? '#fbbf24' : '#9ca3af' }} />
                      </ListItemIcon>
                      <ListItemText
                        primary={tenant.name}
                        secondary={`${tenant.userCount} users • ${tenant.isActive ? 'Active' : 'Inactive'} • Created: ${new Date(tenant.createdAt).toLocaleDateString()}`}
                        primaryTypographyProps={{ color: 'white' }}
                        secondaryTypographyProps={{ color: '#d1d5db' }}
                      />
                      <Chip
                        label={tenant.isActive ? 'Active' : 'Inactive'}
                        sx={{
                          bgcolor: tenant.isActive ? '#10b981' : '#6b7280',
                          color: 'white',
                        }}
                        size="small"
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Tenants Usage Metrics Tile */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, bgcolor: '#374151', color: 'white', border: '1px solid #4b5563' }} elevation={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                Tenants Usage Metrics
              </Typography>
              <Button
                component={NavigationLink}
                href="/super-admin/metrics"
                variant="outlined"
                startIcon={<BarChartIcon />}
                size="small"
                sx={{
                  borderColor: '#fbbf24',
                  color: '#fbbf24',
                  '&:hover': {
                    borderColor: '#f59e0b',
                    bgcolor: 'rgba(251, 191, 36, 0.1)',
                  },
                }}
              >
                Detailed Metrics
              </Button>
            </Box>

            {metricsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                    <CardContent>
                      <Typography sx={{ color: '#d1d5db' }} variant="subtitle2" gutterBottom>
                        Total Recognitions
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: '#fbbf24', fontWeight: 'bold' }}>
                        {aggregateMetrics.totalRecognitions.toLocaleString()}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <ArrowUpwardIcon sx={{ color: '#10b981', fontSize: 16, mr: 0.5 }} />
                        <Typography variant="body2" sx={{ color: '#10b981' }}>
                          {aggregateMetrics.recognitionGrowth}% from last month
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                    <CardContent>
                      <Typography sx={{ color: '#d1d5db' }} variant="subtitle2" gutterBottom>
                        Total Points Issued
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: '#3b82f6', fontWeight: 'bold' }}>
                        {aggregateMetrics.totalPointsIssued.toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                    <CardContent>
                      <Typography sx={{ color: '#d1d5db' }} variant="subtitle2" gutterBottom>
                        Total Points Redeemed
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>
                        {aggregateMetrics.totalPointsRedeemed.toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                    <CardContent>
                      <Typography sx={{ color: '#d1d5db' }} variant="subtitle2" gutterBottom>
                        Avg. Redemption Rate
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: '#10b981', fontWeight: 'bold' }}>
                        {aggregateMetrics.averageRedemptionRate}%
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Tenants User Overview Tile */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2, bgcolor: '#374151', color: 'white', border: '1px solid #4b5563' }} elevation={0}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                Tenants User Overview
              </Typography>
              <Button
                component={NavigationLink}
                href="/super-admin/users"
                variant="outlined"
                startIcon={<PeopleIcon />}
                size="small"
                sx={{
                  borderColor: '#fbbf24',
                  color: '#fbbf24',
                  '&:hover': {
                    borderColor: '#f59e0b',
                    bgcolor: 'rgba(251, 191, 36, 0.1)',
                  },
                }}
              >
                User Management
              </Button>
            </Box>

            {usersLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <PeopleIcon sx={{ fontSize: 40, color: '#fbbf24', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            Total Users
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {userOverview.totalUsers.toLocaleString()}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonAddIcon sx={{ fontSize: 40, color: '#10b981', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            New Users This Month
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {userOverview.newUsersThisMonth}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={0} sx={{ bgcolor: '#4b5563', color: 'white', border: '1px solid #6b7280' }}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <TrendingUpIcon sx={{ fontSize: 40, color: '#3b82f6', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom sx={{ color: '#d1d5db' }}>
                            User Growth Rate
                          </Typography>
                          <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold' }}>
                            {userOverview.userGrowthRate}%
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ color: 'white', fontWeight: 'bold' }}>
                  Top Tenants by User Count
                </Typography>
                <List>
                  {userOverview.topTenantsByUsers.map((tenant, index) => (
                    <ListItem
                      key={tenant.id}
                      component={NavigationLink}
                      href={`/super-admin/tenants/${tenant.id}`}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': { bgcolor: '#4b5563' },
                        color: 'white',
                        textDecoration: 'none',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <Avatar sx={{ bgcolor: '#fbbf24', color: '#000', mr: 2, fontWeight: 'bold' }}>
                        {index + 1}
                      </Avatar>
                      <ListItemText
                        primary={tenant.name}
                        secondary={`${tenant.userCount} users`}
                        primaryTypographyProps={{ color: 'white' }}
                        secondaryTypographyProps={{ color: '#d1d5db' }}
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
