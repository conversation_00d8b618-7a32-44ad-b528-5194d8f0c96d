'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Business as BusinessIcon,
  People as PeopleIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  MonitorHeart as MonitorHeartIcon,
  Payments as PaymentsIcon,
  Tune as TuneIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { getAllTenants } from '@/services/tenantService';
import { getSystemHealth } from '@/services/systemService';
import { TenantDto } from '@/types/tenants';

export default function SuperAdminPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [tenantsLoading, setTenantsLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState<'Healthy' | 'Degraded' | 'Unhealthy' | null>(null);
  const [systemStatusLoading, setSystemStatusLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/dashboard');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const data = await getAllTenants();
        setTenants(data);
      } catch (error) {
        console.error('Error fetching tenants:', error);
      } finally {
        setTenantsLoading(false);
      }
    };

    const fetchSystemHealth = async () => {
      try {
        const data = await getSystemHealth();
        if (data) {
          setSystemStatus(data.status);
        }
      } catch (error) {
        console.error('Error fetching system health:', error);
      } finally {
        setSystemStatusLoading(false);
      }
    };

    fetchTenants();
    fetchSystemHealth();
  }, []);

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  const quickActions = [
    { title: 'SuperAdmin Dashboard', icon: <DashboardIcon />, path: '/super-admin/dashboard' },
    { title: 'Create Tenant', icon: <BusinessIcon />, path: '/super-admin/tenants/create' },
    { title: 'System Config', icon: <TuneIcon />, path: '/super-admin/config' },
    { title: 'View Audit Logs', icon: <SecurityIcon />, path: '/super-admin/audit-logs' },
    { title: 'System Health', icon: <MonitorHeartIcon />, path: '/super-admin/health' },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Super Admin Portal
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          {quickActions.map((action) => (
            <Grid item xs={12} sm={6} md={3} key={action.title}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                  <Box sx={{ mb: 2, color: 'primary.main', fontSize: 40 }}>
                    {action.icon}
                  </Box>
                  <Typography variant="h6" component="div" align="center">
                    {action.title}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    component={Link}
                    href={action.path}
                    fullWidth
                    variant="contained"
                    color="primary"
                  >
                    Go
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }} elevation={2}>
            <Typography variant="h6" gutterBottom>
              Tenant Overview
            </Typography>
            {tenantsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body1">
                    Total Tenants: <strong>{tenants.length}</strong>
                  </Typography>
                  <Typography variant="body1">
                    Active Tenants: <strong>{tenants.filter(t => t.isActive).length}</strong>
                  </Typography>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle1" gutterBottom>
                  Recent Tenants
                </Typography>
                <List>
                  {tenants.slice(0, 5).map((tenant) => (
                    <ListItem
                      key={tenant.id}
                      component={Link}
                      href={`/super-admin/tenants/${tenant.id}`}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { bgcolor: 'action.hover' },
                        color: 'inherit',
                        textDecoration: 'none'
                      }}
                    >
                      <ListItemIcon>
                        <BusinessIcon color={tenant.isActive ? 'primary' : 'disabled'} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={tenant.name} 
                        secondary={`${tenant.userCount} users • ${tenant.isActive ? 'Active' : 'Inactive'}`} 
                      />
                    </ListItem>
                  ))}
                </List>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button 
                    component={Link} 
                    href="/super-admin/tenants"
                    variant="outlined"
                  >
                    View All Tenants
                  </Button>
                </Box>
              </>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }} elevation={2}>
            <Typography variant="h6" gutterBottom>
              System Status
            </Typography>
            {systemStatusLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Box sx={{ mb: 3 }}>
                  {systemStatus === 'Healthy' && (
                    <Alert severity="success">All systems operational</Alert>
                  )}
                  {systemStatus === 'Degraded' && (
                    <Alert severity="warning">Some systems experiencing issues</Alert>
                  )}
                  {systemStatus === 'Unhealthy' && (
                    <Alert severity="error">Critical system issues detected</Alert>
                  )}
                  {systemStatus === null && (
                    <Alert severity="info">System status information unavailable</Alert>
                  )}
                </Box>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <Button 
                    component={Link} 
                    href="/super-admin/health"
                    variant="outlined"
                  >
                    View System Health
                  </Button>
                </Box>
              </>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
