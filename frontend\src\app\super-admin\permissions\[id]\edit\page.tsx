'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { 
  getPermissionById, 
  updatePermission,
  PermissionDto,
  UpdatePermissionRequest,
  isValidPermissionName,
  getUniqueCategories,
  getAllPermissions,
} from '@/services/permissionService';

export default function EditPermissionPage({ params }: { params: { id: string } }) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [permission, setPermission] = useState<PermissionDto | null>(null);
  const [formData, setFormData] = useState<UpdatePermissionRequest>({
    name: '',
    description: '',
    category: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [newCategory, setNewCategory] = useState('');

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch permission data
        const permissionData = await getPermissionById(params.id);
        if (permissionData) {
          setPermission(permissionData);
          setFormData({
            name: permissionData.name,
            description: permissionData.description || '',
            category: permissionData.category,
          });
          
          // Fetch all permissions to get categories
          const allPermissions = await getAllPermissions();
          const uniqueCategories = getUniqueCategories(allPermissions);
          setCategories(uniqueCategories);
        } else {
          setError('Permission not found');
        }
      } catch (error) {
        console.error('Error fetching permission details:', error);
        setError('Failed to load permission details');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: string };
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Validate name field in real-time
    if (name === 'name' && value && !isValidPermissionName(value)) {
      setErrors(prev => ({
        ...prev,
        name: 'Name can only contain letters, numbers, and underscores'
      }));
    }
  };

  const handleNewCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewCategory(e.target.value);
  };

  const handleAddNewCategory = () => {
    if (newCategory.trim() && !categories.includes(newCategory)) {
      setCategories([...categories, newCategory].sort());
      setFormData(prev => ({
        ...prev,
        category: newCategory
      }));
      setNewCategory('');
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (!isValidPermissionName(formData.name)) {
      newErrors.name = 'Name can only contain letters, numbers, and underscores';
    }
    
    // Validate category
    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setSubmitting(true);
    setError(null);
    
    try {
      const result = await updatePermission(params.id, formData);
      
      if (result) {
        setSuccess('Permission updated successfully');
        setPermission(result);
        
        // Clear success message after a delay
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError('Failed to update permission. Please try again.');
      }
    } catch (error: any) {
      console.error('Error updating permission:', error);
      setError(error.response?.data || 'An unexpected error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !permission) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          component={Link}
          href="/super-admin/permissions"
          startIcon={<ArrowBackIcon />}
        >
          Back to Permissions
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href={`/super-admin/permissions/${params.id}`}
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Permission Details
        </Button>
        <Typography variant="h4">
          Edit Permission
        </Typography>
      </Box>
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }} elevation={2}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name || 'Name can only contain letters, numbers, and underscores'}
                disabled={submitting}
                required
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth error={!!errors.category}>
                <InputLabel id="edit-category-label">Category</InputLabel>
                <Select
                  labelId="edit-category-label"
                  name="category"
                  value={formData.category}
                  label="Category"
                  onChange={handleChange as any}
                  disabled={submitting}
                  required
                >
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
                {errors.category && (
                  <FormHelperText>{errors.category}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TextField
                  fullWidth
                  label="New Category"
                  value={newCategory}
                  onChange={handleNewCategoryChange}
                  disabled={submitting}
                  placeholder="Enter new category name"
                />
                <Button
                  variant="outlined"
                  onClick={handleAddNewCategory}
                  disabled={!newCategory.trim() || submitting}
                  startIcon={<CategoryIcon />}
                >
                  Add
                </Button>
              </Box>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                disabled={submitting}
                multiline
                rows={3}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="button"
                  variant="outlined"
                  onClick={() => router.push(`/super-admin/permissions/${params.id}`)}
                  sx={{ mr: 2 }}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={submitting}
                  startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
                >
                  Save Changes
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Box>
  );
}
