'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Category as CategoryIcon,
  Description as DescriptionIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { 
  getPermissionById, 
  deletePermission,
  PermissionDto,
  formatPermissionName,
} from '@/services/permissionService';

export default function PermissionDetailsPage({ params }: { params: { id: string } }) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [permission, setPermission] = useState<PermissionDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchPermissionDetails = async () => {
      try {
        const data = await getPermissionById(params.id);
        if (data) {
          setPermission(data);
        } else {
          setError('Permission not found');
        }
      } catch (error) {
        console.error('Error fetching permission details:', error);
        setError('Failed to load permission details');
      } finally {
        setLoading(false);
      }
    };

    fetchPermissionDetails();
  }, [params.id]);

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
  };

  const handleDeletePermission = async () => {
    if (!permission) return;
    
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      const success = await deletePermission(permission.id);
      
      if (success) {
        setDeleteDialogOpen(false);
        
        // Redirect to permissions list after a short delay
        setTimeout(() => {
          router.push('/super-admin/permissions');
        }, 1500);
        
        setActionSuccess('Permission deleted successfully. Redirecting...');
      }
    } catch (error: any) {
      console.error('Error deleting permission:', error);
      setActionError(error.response?.data || 'Failed to delete permission. It may be in use by roles.');
    } finally {
      setActionInProgress(false);
    }
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !permission) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || 'Permission not found'}
        </Alert>
        <Button
          component={Link}
          href="/super-admin/permissions"
          startIcon={<ArrowBackIcon />}
        >
          Back to Permissions
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href="/super-admin/permissions"
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Permissions
        </Button>
        <Typography variant="h4">
          Permission Details
        </Typography>
      </Box>

      {actionSuccess && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {actionSuccess}
        </Alert>
      )}

      {actionError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {actionError}
        </Alert>
      )}

      <Paper sx={{ p: 3 }} elevation={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography variant="h5">{permission.name}</Typography>
            <Typography variant="subtitle1" color="text.secondary">
              {formatPermissionName(permission.name)}
            </Typography>
          </Box>
          <Box>
            <Tooltip title="Edit Permission">
              <IconButton 
                component={Link} 
                href={`/super-admin/permissions/${permission.id}/edit`}
                color="primary"
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete Permission">
              <IconButton
                onClick={handleDeleteClick}
                color="error"
                disabled={actionInProgress}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CategoryIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="subtitle2" color="text.secondary">
                Category
              </Typography>
            </Box>
            <Chip 
              label={permission.category} 
              color="primary" 
              variant="outlined"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
            </Box>
            <Typography variant="body1">
              {new Date(permission.createdAt).toLocaleDateString()}
            </Typography>
          </Grid>
          
          {permission.updatedAt && (
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="text.secondary">
                  Last Updated
                </Typography>
              </Box>
              <Typography variant="body1">
                {new Date(permission.updatedAt).toLocaleDateString()}
              </Typography>
            </Grid>
          )}
          
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <DescriptionIcon sx={{ mr: 1, color: 'text.secondary' }} />
              <Typography variant="subtitle2" color="text.secondary">
                Description
              </Typography>
            </Box>
            <Typography variant="body1">
              {permission.description || 'No description provided'}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the permission "{permission.name}"? This action cannot be undone.
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Category:</strong> {permission.category}
              </Typography>
              {permission.description && (
                <Typography variant="body2">
                  <strong>Description:</strong> {permission.description}
                </Typography>
              )}
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeletePermission} 
            color="error" 
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : null}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
