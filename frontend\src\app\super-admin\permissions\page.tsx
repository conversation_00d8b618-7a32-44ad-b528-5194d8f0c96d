'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Tooltip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Grid,
  FormHelperText,
  SelectChangeEvent,
  Tab,
  Tabs,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
  Category as CategoryIcon,
} from '@mui/icons-material';
import {
  getAllPermissions,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission,
  PermissionDto,
  CreatePermissionRequest,
  UpdatePermissionRequest,
  isValidPermissionName,
  isValidCategoryName,
  getUniqueCategories,
  formatPermissionName,
} from '@/services/permissionService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`permissions-tabpanel-${index}`}
      aria-labelledby={`permissions-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function PermissionsPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  // Permissions state
  const [permissions, setPermissions] = useState<PermissionDto[]>([]);
  const [filteredPermissions, setFilteredPermissions] = useState<PermissionDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [categories, setCategories] = useState<string[]>([]);

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<PermissionDto | null>(null);

  // Form states
  const [createFormData, setCreateFormData] = useState<CreatePermissionRequest>({
    name: '',
    description: '',
    category: '',
  });
  const [editFormData, setEditFormData] = useState<UpdatePermissionRequest>({
    name: '',
    description: '',
    category: '',
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Action states
  const [actionInProgress, setActionInProgress] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);
  const [newCategory, setNewCategory] = useState('');

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  // Fetch permissions
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const data = await getAllPermissions();
        setPermissions(data);
        setFilteredPermissions(data);

        // Extract unique categories
        const uniqueCategories = getUniqueCategories(data);
        setCategories(uniqueCategories);
      } catch (error) {
        console.error('Error fetching permissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, []);

  // Apply filters when filter values change
  useEffect(() => {
    let filtered = [...permissions];

    // Apply search filter
    if (searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(permission =>
        permission.name.toLowerCase().includes(searchLower) ||
        (permission.description?.toLowerCase().includes(searchLower) || false) ||
        permission.category.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(permission => permission.category === selectedCategory);
    }

    setFilteredPermissions(filtered);
  }, [searchTerm, selectedCategory, permissions]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleCategoryFilterChange = (event: SelectChangeEvent) => {
    setSelectedCategory(event.target.value);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedCategory('all');
  };

  // Create permission handlers
  const handleCreateDialogOpen = () => {
    setCreateFormData({
      name: '',
      description: '',
      category: categories.length > 0 ? categories[0] : '',
    });
    setFormErrors({});
    setNewCategory('');
    setCreateDialogOpen(true);
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
  };

  const handleCreateFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: string };
    setCreateFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Validate name field in real-time
    if (name === 'name' && value && !isValidPermissionName(value)) {
      setFormErrors(prev => ({
        ...prev,
        name: 'Name can only contain letters, numbers, and underscores'
      }));
    }
  };

  const validateCreateForm = () => {
    const errors: Record<string, string> = {};

    if (!createFormData.name.trim()) {
      errors.name = 'Name is required';
    } else if (!isValidPermissionName(createFormData.name)) {
      errors.name = 'Name can only contain letters, numbers, and underscores';
    }

    if (!createFormData.category.trim()) {
      errors.category = 'Category is required';
    }

    // Validate new category if selected
    if (createFormData.category === 'new') {
      if (!newCategory.trim()) {
        errors.newCategory = 'New category name is required';
      } else if (!isValidCategoryName(newCategory)) {
        errors.newCategory = 'Category name should contain only text (alphabetical characters)';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreatePermission = async () => {
    if (!validateCreateForm()) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      // If "new" category is selected, use the newCategory value
      const formData = { ...createFormData };
      if (formData.category === 'new' && newCategory.trim()) {
        formData.category = newCategory.trim();
      }

      const result = await createPermission(formData);

      if (result) {
        // Add the new permission to the list
        setPermissions([...permissions, result]);

        // Update categories if a new one was added
        if (!categories.includes(result.category)) {
          setCategories([...categories, result.category].sort());
        }

        setCreateDialogOpen(false);
        setActionSuccess('Permission created successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error creating permission:', error);
      setActionError(error.response?.data || 'Failed to create permission');
    } finally {
      setActionInProgress(false);
    }
  };

  // Edit permission handlers
  const handleEditClick = (permission: PermissionDto) => {
    setSelectedPermission(permission);
    setEditFormData({
      name: permission.name,
      description: permission.description || '',
      category: permission.category,
    });
    setFormErrors({});
    setEditDialogOpen(true);
  };

  const handleEditDialogClose = () => {
    setEditDialogOpen(false);
    setSelectedPermission(null);
  };

  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: string };
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Validate name field in real-time
    if (name === 'name' && value && !isValidPermissionName(value)) {
      setFormErrors(prev => ({
        ...prev,
        name: 'Name can only contain letters, numbers, and underscores'
      }));
    }
  };

  const validateEditForm = () => {
    const errors: Record<string, string> = {};

    if (!editFormData.name.trim()) {
      errors.name = 'Name is required';
    } else if (!isValidPermissionName(editFormData.name)) {
      errors.name = 'Name can only contain letters, numbers, and underscores';
    }

    if (!editFormData.category.trim()) {
      errors.category = 'Category is required';
    }

    // Validate new category if selected
    if (editFormData.category === 'new') {
      if (!newCategory.trim()) {
        errors.newCategory = 'New category name is required';
      } else if (!isValidCategoryName(newCategory)) {
        errors.newCategory = 'Category name should contain only text (alphabetical characters)';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleUpdatePermission = async () => {
    if (!selectedPermission || !validateEditForm()) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      // If "new" category is selected, use the newCategory value
      const formData = { ...editFormData };
      if (formData.category === 'new' && newCategory.trim()) {
        formData.category = newCategory.trim();
      }

      const result = await updatePermission(selectedPermission.id, formData);

      if (result) {
        // Update the permission in the list
        setPermissions(permissions.map(p =>
          p.id === selectedPermission.id ? result : p
        ));

        // Update categories if a new one was added
        if (!categories.includes(result.category)) {
          setCategories([...categories, result.category].sort());
        }

        setEditDialogOpen(false);
        setSelectedPermission(null);
        setActionSuccess('Permission updated successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error updating permission:', error);
      setActionError(error.response?.data || 'Failed to update permission');
    } finally {
      setActionInProgress(false);
    }
  };

  // Delete permission handlers
  const handleDeleteClick = (permission: PermissionDto) => {
    setSelectedPermission(permission);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedPermission(null);
  };

  const handleDeletePermission = async () => {
    if (!selectedPermission) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      const success = await deletePermission(selectedPermission.id);

      if (success) {
        // Remove the permission from the list
        setPermissions(permissions.filter(p => p.id !== selectedPermission.id));

        setDeleteDialogOpen(false);
        setSelectedPermission(null);
        setActionSuccess('Permission deleted successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error deleting permission:', error);
      setActionError(error.response?.data || 'Failed to delete permission. It may be in use by roles.');
    } finally {
      setActionInProgress(false);
    }
  };

  // New category handlers
  const handleNewCategoryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewCategory(e.target.value);
  };

  const handleAddNewCategory = () => {
    if (newCategory.trim() && !categories.includes(newCategory)) {
      setCategories([...categories, newCategory].sort());
      setCreateFormData(prev => ({
        ...prev,
        category: newCategory
      }));
      setNewCategory('');
    }
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Permissions Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateDialogOpen}
        >
          Create Permission
        </Button>
      </Box>

      {actionSuccess && (
        <Box sx={{ mb: 2 }}>
          <Chip
            label={actionSuccess}
            color="success"
            onDelete={() => setActionSuccess(null)}
          />
        </Box>
      )}

      {actionError && (
        <Box sx={{ mb: 2 }}>
          <Chip
            label={actionError}
            color="error"
            onDelete={() => setActionError(null)}
          />
        </Box>
      )}

      <Paper sx={{ p: 2, mb: 3 }} elevation={2}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="permissions tabs">
            <Tab label="All Permissions" id="permissions-tab-0" />
            <Tab label="By Category" id="permissions-tab-1" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setSearchTerm('')} size="small">
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>

          <TableContainer sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : filteredPermissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No permissions found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPermissions.map((permission) => (
                    <TableRow key={permission.id} hover>
                      <TableCell>{permission.name}</TableCell>
                      <TableCell>
                        <Chip
                          label={permission.category}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{permission.description || '-'}</TableCell>
                      <TableCell>
                        {new Date(permission.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Edit">
                          <IconButton
                            onClick={() => handleEditClick(permission)}
                            color="primary"
                            disabled={actionInProgress}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            onClick={() => handleDeleteClick(permission)}
                            color="error"
                            disabled={actionInProgress}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="category-filter-label">Category</InputLabel>
                <Select
                  labelId="category-filter-label"
                  value={selectedCategory}
                  label="Category"
                  onChange={handleCategoryFilterChange}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm && (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setSearchTerm('')} size="small">
                        <ClearIcon />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>

          <TableContainer sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress size={24} />
                    </TableCell>
                  </TableRow>
                ) : filteredPermissions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No permissions found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPermissions.map((permission) => (
                    <TableRow key={permission.id} hover>
                      <TableCell>{permission.name}</TableCell>
                      <TableCell>
                        <Chip
                          label={permission.category}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{permission.description || '-'}</TableCell>
                      <TableCell>
                        {new Date(permission.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Edit">
                          <IconButton
                            onClick={() => handleEditClick(permission)}
                            color="primary"
                            disabled={actionInProgress}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            onClick={() => handleDeleteClick(permission)}
                            color="error"
                            disabled={actionInProgress}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>
      </Paper>

      {/* Create Permission Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCreateDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Permission</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={createFormData.name}
                  onChange={handleCreateFormChange}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Name can only contain letters, numbers, and underscores'}
                  disabled={actionInProgress}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth error={!!formErrors.category}>
                  <InputLabel id="create-category-label">Category</InputLabel>
                  <Select
                    labelId="create-category-label"
                    name="category"
                    value={createFormData.category}
                    label="Category"
                    onChange={handleCreateFormChange as any}
                    disabled={actionInProgress}
                    required
                  >
                    <MenuItem value="new">
                      <em>New Category</em>
                    </MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.category && (
                    <FormHelperText>{formErrors.category}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {createFormData.category === 'new' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="New Category Name"
                    name="newCategory"
                    value={newCategory}
                    onChange={handleNewCategoryChange}
                    error={!!formErrors.newCategory}
                    helperText={formErrors.newCategory || 'Category name should contain only text (alphabetical characters)'}
                    disabled={actionInProgress}
                    required
                  />
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={createFormData.description}
                  onChange={handleCreateFormChange}
                  disabled={actionInProgress}
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleCreatePermission}
            color="primary"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Permission Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleEditDialogClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit Permission</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={editFormData.name}
                  onChange={handleEditFormChange}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Name can only contain letters, numbers, and underscores'}
                  disabled={actionInProgress}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth error={!!formErrors.category}>
                  <InputLabel id="edit-category-label">Category</InputLabel>
                  <Select
                    labelId="edit-category-label"
                    name="category"
                    value={editFormData.category}
                    label="Category"
                    onChange={handleEditFormChange as any}
                    disabled={actionInProgress}
                    required
                  >
                    <MenuItem value="new">
                      <em>New Category</em>
                    </MenuItem>
                    {categories.map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.category && (
                    <FormHelperText>{formErrors.category}</FormHelperText>
                  )}
                </FormControl>
              </Grid>

              {editFormData.category === 'new' ? (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="New Category Name"
                    name="newCategory"
                    value={newCategory}
                    onChange={handleNewCategoryChange}
                    error={!!formErrors.newCategory}
                    helperText={formErrors.newCategory || 'Category name should contain only text (alphabetical characters)'}
                    disabled={actionInProgress}
                    required
                  />
                </Grid>
              ) : (
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TextField
                      fullWidth
                      label="New Category"
                      value={newCategory}
                      onChange={handleNewCategoryChange}
                      disabled={actionInProgress}
                      placeholder="Enter new category name"
                    />
                    <Button
                      variant="outlined"
                      onClick={handleAddNewCategory}
                      disabled={!newCategory.trim() || actionInProgress}
                      startIcon={<CategoryIcon />}
                    >
                      Add
                    </Button>
                  </Box>
                </Grid>
              )}

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={editFormData.description}
                  onChange={handleEditFormChange}
                  disabled={actionInProgress}
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdatePermission}
            color="primary"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the permission "{selectedPermission?.name}"? This action cannot be undone.
            {selectedPermission && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Category:</strong> {selectedPermission.category}
                </Typography>
                {selectedPermission.description && (
                  <Typography variant="body2">
                    <strong>Description:</strong> {selectedPermission.description}
                  </Typography>
                )}
              </Box>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleDeletePermission}
            color="error"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : null}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
