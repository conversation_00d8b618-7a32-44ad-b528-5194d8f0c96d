'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Tooltip,
  FormControl,
  InputLabel,
  Grid,
  FormHelperText,
  Checkbox,
  FormGroup,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
  VpnKey as VpnKeyIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from '@mui/icons-material';
import {
  getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  RoleDto,
  CreateRoleRequest,
  UpdateRoleRequest,
} from '@/services/roleService';
import {
  getAllPermissions,
  PermissionDto,
  getUniqueCategories,
} from '@/services/permissionService';

export default function RolesPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();

  // Roles state
  const [roles, setRoles] = useState<RoleDto[]>([]);
  const [filteredRoles, setFilteredRoles] = useState<RoleDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Permissions state
  const [permissions, setPermissions] = useState<PermissionDto[]>([]);
  const [permissionCategories, setPermissionCategories] = useState<string[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleDto | null>(null);

  // Form states
  const [createFormData, setCreateFormData] = useState<CreateRoleRequest>({
    name: '',
    description: '',
    permissions: [],
  });
  const [editFormData, setEditFormData] = useState<UpdateRoleRequest>({
    name: '',
    description: '',
    permissions: [],
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Action states
  const [actionInProgress, setActionInProgress] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  // Fetch roles
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const data = await getAllRoles();
        setRoles(data);
        setFilteredRoles(data);
      } catch (error) {
        console.error('Error fetching roles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRoles();
  }, []);

  // Fetch permissions
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const data = await getAllPermissions();
        setPermissions(data);

        // Extract unique categories
        const uniqueCategories = getUniqueCategories(data);
        setPermissionCategories(uniqueCategories);
      } catch (error) {
        console.error('Error fetching permissions:', error);
      } finally {
        setPermissionsLoading(false);
      }
    };

    fetchPermissions();
  }, []);

  // Apply filters when filter values change
  useEffect(() => {
    let filtered = [...roles];

    // Apply search filter
    if (searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(role =>
        role.name.toLowerCase().includes(searchLower) ||
        (role.description?.toLowerCase().includes(searchLower) || false)
      );
    }

    setFilteredRoles(filtered);
  }, [searchTerm, roles]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Toggle category expansion
  const toggleCategoryExpansion = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Toggle all categories expansion
  const toggleAllCategories = (expand: boolean) => {
    const newState: Record<string, boolean> = {};
    permissionCategories.forEach(category => {
      newState[category] = expand;
    });
    setExpandedCategories(newState);
  };

  // Initialize expanded categories when dialog opens
  const initializeExpandedCategories = () => {
    const initialState: Record<string, boolean> = {};
    permissionCategories.forEach(category => {
      initialState[category] = true; // Start with all categories expanded
    });
    setExpandedCategories(initialState);
  };

  // Create role handlers
  const handleCreateDialogOpen = () => {
    setCreateFormData({
      name: '',
      description: '',
      permissions: [],
    });
    setFormErrors({});
    initializeExpandedCategories();
    setCreateDialogOpen(true);
  };

  const handleCreateDialogClose = () => {
    setCreateDialogOpen(false);
  };

  const handleCreateFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: string };
    setCreateFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleCreatePermissionsChange = (selectedPermissions: string[]) => {
    setCreateFormData(prev => ({
      ...prev,
      permissions: selectedPermissions
    }));

    // Clear error when permissions are selected
    if (formErrors['permissions']) {
      setFormErrors(prev => ({
        ...prev,
        permissions: ''
      }));
    }
  };

  const validateCreateForm = () => {
    const errors: Record<string, string> = {};

    if (!createFormData.name.trim()) {
      errors.name = 'Name is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateRole = async () => {
    if (!validateCreateForm()) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      const result = await createRole(createFormData);

      if (result) {
        // Add the new role to the list
        setRoles([...roles, result]);

        setCreateDialogOpen(false);
        setActionSuccess('Role created successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error creating role:', error);
      setActionError(error.response?.data || 'Failed to create role');
    } finally {
      setActionInProgress(false);
    }
  };

  // Edit role handlers
  const handleEditClick = (role: RoleDto) => {
    setSelectedRole(role);
    setEditFormData({
      name: role.name,
      description: role.description || '',
      permissions: role.permissions,
    });
    setFormErrors({});
    initializeExpandedCategories();
    setEditDialogOpen(true);
  };

  const handleEditDialogClose = () => {
    setEditDialogOpen(false);
    setSelectedRole(null);
  };

  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target as { name: string; value: string };
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleEditPermissionsChange = (selectedPermissions: string[]) => {
    setEditFormData(prev => ({
      ...prev,
      permissions: selectedPermissions
    }));
  };

  const validateEditForm = () => {
    const errors: Record<string, string> = {};

    if (!editFormData.name?.trim()) {
      errors.name = 'Name is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleUpdateRole = async () => {
    if (!selectedRole || !validateEditForm()) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      const result = await updateRole(selectedRole.id, editFormData);

      if (result) {
        // Update the role in the list
        setRoles(roles.map(r =>
          r.id === selectedRole.id ? result : r
        ));

        setEditDialogOpen(false);
        setSelectedRole(null);
        setActionSuccess('Role updated successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error updating role:', error);
      setActionError(error.response?.data || 'Failed to update role');
    } finally {
      setActionInProgress(false);
    }
  };

  // Delete role handlers
  const handleDeleteClick = (role: RoleDto) => {
    setSelectedRole(role);
    setDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setDeleteDialogOpen(false);
    setSelectedRole(null);
  };

  const handleDeleteRole = async () => {
    if (!selectedRole) {
      return;
    }

    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);

    try {
      const success = await deleteRole(selectedRole.id);

      if (success) {
        // Remove the role from the list
        setRoles(roles.filter(r => r.id !== selectedRole.id));

        setDeleteDialogOpen(false);
        setSelectedRole(null);
        setActionSuccess('Role deleted successfully');

        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      }
    } catch (error: any) {
      console.error('Error deleting role:', error);
      setActionError(error.response?.data || 'Failed to delete role. It may be assigned to users.');
    } finally {
      setActionInProgress(false);
    }
  };

  // Group permissions by category for better display
  const getPermissionsByCategory = () => {
    const grouped: Record<string, PermissionDto[]> = {};

    permissions.forEach(permission => {
      if (!grouped[permission.category]) {
        grouped[permission.category] = [];
      }
      grouped[permission.category].push(permission);
    });

    return grouped;
  };

  // Check if a permission is selected
  const isPermissionSelected = (permissionName: string, selectedPermissions: string[]) => {
    return selectedPermissions.includes(permissionName);
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          Roles Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateDialogOpen}
        >
          Create Role
        </Button>
      </Box>

      {actionSuccess && (
        <Box sx={{ mb: 2 }}>
          <Chip
            label={actionSuccess}
            color="success"
            onDelete={() => setActionSuccess(null)}
          />
        </Box>
      )}

      {actionError && (
        <Box sx={{ mb: 2 }}>
          <Chip
            label={actionError}
            color="error"
            onDelete={() => setActionError(null)}
          />
        </Box>
      )}

      <Paper sx={{ p: 2, mb: 3 }} elevation={2}>
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search roles..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setSearchTerm('')} size="small">
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Permissions</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : filteredRoles.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No roles found
                  </TableCell>
                </TableRow>
              ) : (
                filteredRoles.map((role) => (
                  <TableRow key={role.id} hover>
                    <TableCell>{role.name}</TableCell>
                    <TableCell>{role.description || '-'}</TableCell>
                    <TableCell>
                      <Chip
                        label={`${role.permissions.length} permissions`}
                        color="primary"
                        variant="outlined"
                        size="small"
                        icon={<VpnKeyIcon />}
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(role.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit">
                        <IconButton
                          onClick={() => handleEditClick(role)}
                          color="primary"
                          disabled={actionInProgress}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => handleDeleteClick(role)}
                          color="error"
                          disabled={actionInProgress}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Create Role Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCreateDialogClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Role</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={createFormData.name}
                  onChange={handleCreateFormChange}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Enter a unique role name'}
                  disabled={actionInProgress}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={createFormData.description}
                  onChange={handleCreateFormChange}
                  disabled={actionInProgress}
                  multiline
                  rows={2}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1">
                    Permissions
                  </Typography>
                  <Box>
                    <Button
                      size="small"
                      onClick={() => toggleAllCategories(true)}
                      startIcon={<ExpandLessIcon />}
                      sx={{ mr: 1 }}
                    >
                      Expand All
                    </Button>
                    <Button
                      size="small"
                      onClick={() => toggleAllCategories(false)}
                      startIcon={<ExpandMoreIcon />}
                    >
                      Collapse All
                    </Button>
                  </Box>
                </Box>
                {permissionsLoading ? (
                  <CircularProgress size={24} />
                ) : (
                  <Box sx={{ maxHeight: '300px', overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1, p: 2 }}>
                    {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                      <Box key={category} sx={{ mb: 2 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            mb: 1,
                            '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' },
                            borderRadius: 1,
                            p: 0.5
                          }}
                          onClick={() => toggleCategoryExpansion(category)}
                        >
                          {expandedCategories[category] ? <ExpandLessIcon color="primary" /> : <ExpandMoreIcon color="primary" />}
                          <Typography variant="subtitle2" color="primary">
                            {category} ({categoryPermissions.length})
                          </Typography>
                        </Box>

                        {expandedCategories[category] && (
                          <FormGroup sx={{ ml: 4 }}>
                            {categoryPermissions.map(permission => (
                              <FormControlLabel
                                key={permission.id}
                                control={
                                  <Checkbox
                                    checked={isPermissionSelected(permission.name, createFormData.permissions)}
                                    onChange={(e) => {
                                      const newPermissions = e.target.checked
                                        ? [...createFormData.permissions, permission.name]
                                        : createFormData.permissions.filter(p => p !== permission.name);
                                      handleCreatePermissionsChange(newPermissions);
                                    }}
                                    disabled={actionInProgress}
                                  />
                                }
                                label={
                                  <Box>
                                    <Typography variant="body2">{permission.name}</Typography>
                                    {permission.description && (
                                      <Typography variant="caption" color="text.secondary">
                                        {permission.description}
                                      </Typography>
                                    )}
                                  </Box>
                                }
                              />
                            ))}
                          </FormGroup>
                        )}
                      </Box>
                    ))}
                  </Box>
                )}
                {formErrors.permissions && (
                  <FormHelperText error>{formErrors.permissions}</FormHelperText>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCreateDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateRole}
            color="primary"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleEditDialogClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Edit Role</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={editFormData.name}
                  onChange={handleEditFormChange}
                  error={!!formErrors.name}
                  helperText={formErrors.name || 'Enter a unique role name'}
                  disabled={actionInProgress}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  name="description"
                  value={editFormData.description}
                  onChange={handleEditFormChange}
                  disabled={actionInProgress}
                  multiline
                  rows={2}
                />
              </Grid>

              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1">
                    Permissions
                  </Typography>
                  <Box>
                    <Button
                      size="small"
                      onClick={() => toggleAllCategories(true)}
                      startIcon={<ExpandLessIcon />}
                      sx={{ mr: 1 }}
                    >
                      Expand All
                    </Button>
                    <Button
                      size="small"
                      onClick={() => toggleAllCategories(false)}
                      startIcon={<ExpandMoreIcon />}
                    >
                      Collapse All
                    </Button>
                  </Box>
                </Box>
                {permissionsLoading ? (
                  <CircularProgress size={24} />
                ) : (
                  <Box sx={{ maxHeight: '300px', overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1, p: 2 }}>
                    {Object.entries(getPermissionsByCategory()).map(([category, categoryPermissions]) => (
                      <Box key={category} sx={{ mb: 2 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            cursor: 'pointer',
                            mb: 1,
                            '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' },
                            borderRadius: 1,
                            p: 0.5
                          }}
                          onClick={() => toggleCategoryExpansion(category)}
                        >
                          {expandedCategories[category] ? <ExpandLessIcon color="primary" /> : <ExpandMoreIcon color="primary" />}
                          <Typography variant="subtitle2" color="primary">
                            {category} ({categoryPermissions.length})
                          </Typography>
                        </Box>

                        {expandedCategories[category] && (
                          <FormGroup sx={{ ml: 4 }}>
                            {categoryPermissions.map(permission => (
                              <FormControlLabel
                                key={permission.id}
                                control={
                                  <Checkbox
                                    checked={isPermissionSelected(permission.name, editFormData.permissions || [])}
                                    onChange={(e) => {
                                      const newPermissions = e.target.checked
                                        ? [...(editFormData.permissions || []), permission.name]
                                        : (editFormData.permissions || []).filter(p => p !== permission.name);
                                      handleEditPermissionsChange(newPermissions);
                                    }}
                                    disabled={actionInProgress}
                                  />
                                }
                                label={
                                  <Box>
                                    <Typography variant="body2">{permission.name}</Typography>
                                    {permission.description && (
                                      <Typography variant="caption" color="text.secondary">
                                        {permission.description}
                                      </Typography>
                                    )}
                                  </Box>
                                }
                              />
                            ))}
                          </FormGroup>
                        )}
                      </Box>
                    ))}
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleUpdateRole}
            color="primary"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the role "{selectedRole?.name}"? This action cannot be undone.
            {selectedRole && (
              <Box sx={{ mt: 2 }}>
                {selectedRole.description && (
                  <Typography variant="body2">
                    <strong>Description:</strong> {selectedRole.description}
                  </Typography>
                )}
                <Typography variant="body2">
                  <strong>Permissions:</strong> {selectedRole.permissions.length}
                </Typography>
              </Box>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDialogClose} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteRole}
            color="error"
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : null}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
