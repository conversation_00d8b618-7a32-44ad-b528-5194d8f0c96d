'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  InputAdornment,
  IconButton,
  FormHelperText,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { getTenantById, updateTenant } from '@/services/tenantService';
import { getUsersByTenantId, updateUser } from '@/services/userService';
import { TenantDto, UpdateTenantRequest } from '@/types/tenants';
import { User } from '@/types/auth';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tenant-edit-tabpanel-${index}`}
      aria-labelledby={`tenant-edit-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

export default function EditTenantPage({ params }: { params: { id: string } }) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [tabValue, setTabValue] = useState(0);
  const [tenant, setTenant] = useState<TenantDto | null>(null);
  const [adminUser, setAdminUser] = useState<User | null>(null);
  const [tenantFormData, setTenantFormData] = useState<UpdateTenantRequest>({
    name: '',
    logoUrl: '',
    isActive: true,
  });
  const [adminFormData, setAdminFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    isActive: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchTenantAndAdminData = async () => {
      try {
        // Fetch tenant data
        const tenantData = await getTenantById(params.id);
        if (tenantData) {
          setTenant(tenantData);
          setTenantFormData({
            name: tenantData.name,
            logoUrl: tenantData.logoUrl || '',
            isActive: tenantData.isActive,
          });
          
          // Fetch users for this tenant
          const users = await getUsersByTenantId(params.id);
          // Find admin user
          const admin = users.find(u => u.roles.includes('Admin'));
          if (admin) {
            setAdminUser(admin);
            setAdminFormData({
              firstName: admin.firstName,
              lastName: admin.lastName,
              email: admin.email,
              isActive: admin.isActive,
            });
          }
        } else {
          setError('Tenant not found');
        }
      } catch (error) {
        console.error('Error fetching tenant details:', error);
        setError('Failed to load tenant details');
      } finally {
        setLoading(false);
      }
    };

    fetchTenantAndAdminData();
  }, [params.id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTenantChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setTenantFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' ? checked : value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleAdminChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setAdminFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' ? checked : value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateTenantForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Validate tenant name
    if (!tenantFormData.name.trim()) {
      newErrors.name = 'Tenant name is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateAdminForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Validate admin first name
    if (!adminFormData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }
    
    // Validate admin last name
    if (!adminFormData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }
    
    // Validate admin email
    if (!adminFormData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(adminFormData.email)) {
      newErrors.email = 'Invalid email format';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTenantSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateTenantForm()) {
      return;
    }
    
    setSubmitting(true);
    setError(null);
    
    try {
      const result = await updateTenant(params.id, tenantFormData);
      
      if (result) {
        setSuccess('Tenant updated successfully');
        setTenant(result);
        // Clear success message after a delay
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError('Failed to update tenant. Please try again.');
      }
    } catch (error) {
      console.error('Error updating tenant:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleAdminSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAdminForm() || !adminUser) {
      return;
    }
    
    setSubmitting(true);
    setError(null);
    
    try {
      const result = await updateUser(adminUser.id, adminFormData);
      
      if (result) {
        setSuccess('Admin user updated successfully');
        setAdminUser(result);
        // Clear success message after a delay
        setTimeout(() => {
          setSuccess(null);
        }, 3000);
      } else {
        setError('Failed to update admin user. Please try again.');
      }
    } catch (error) {
      console.error('Error updating admin user:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error && !tenant) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button
          component={Link}
          href="/super-admin/tenants"
          startIcon={<ArrowBackIcon />}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href={`/super-admin/tenants/${params.id}`}
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Tenant Details
        </Button>
        <Typography variant="h4">
          Edit Tenant
        </Typography>
      </Box>
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3 }} elevation={2}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="tenant edit tabs">
            <Tab label="Tenant Information" id="tenant-edit-tab-0" aria-controls="tenant-edit-tabpanel-0" />
            <Tab label="Admin User" id="tenant-edit-tab-1" aria-controls="tenant-edit-tabpanel-1" />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          <form onSubmit={handleTenantSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tenant Name"
                  name="name"
                  value={tenantFormData.name}
                  onChange={handleTenantChange}
                  error={!!errors.name}
                  helperText={errors.name}
                  disabled={submitting}
                  required
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Logo URL (optional)"
                  name="logoUrl"
                  value={tenantFormData.logoUrl}
                  onChange={handleTenantChange}
                  disabled={submitting}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={tenantFormData.isActive}
                      onChange={handleTenantChange}
                      name="isActive"
                      color="primary"
                      disabled={submitting}
                    />
                  }
                  label="Active"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    type="button"
                    variant="outlined"
                    onClick={() => router.push(`/super-admin/tenants/${params.id}`)}
                    sx={{ mr: 2 }}
                    disabled={submitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={submitting}
                    startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
                  >
                    Save Changes
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </form>
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          {adminUser ? (
            <form onSubmit={handleAdminSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    name="firstName"
                    value={adminFormData.firstName}
                    onChange={handleAdminChange}
                    error={!!errors.firstName}
                    helperText={errors.firstName}
                    disabled={submitting}
                    required
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    name="lastName"
                    value={adminFormData.lastName}
                    onChange={handleAdminChange}
                    error={!!errors.lastName}
                    helperText={errors.lastName}
                    disabled={submitting}
                    required
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    name="email"
                    type="email"
                    value={adminFormData.email}
                    onChange={handleAdminChange}
                    error={!!errors.email}
                    helperText={errors.email}
                    disabled={submitting}
                    required
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={adminFormData.isActive}
                        onChange={handleAdminChange}
                        name="isActive"
                        color="primary"
                        disabled={submitting}
                      />
                    }
                    label="Active"
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      type="button"
                      variant="outlined"
                      onClick={() => router.push(`/super-admin/tenants/${params.id}`)}
                      sx={{ mr: 2 }}
                      disabled={submitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      disabled={submitting}
                      startIcon={submitting ? <CircularProgress size={20} /> : <SaveIcon />}
                    >
                      Save Changes
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          ) : (
            <Alert severity="warning">
              No admin user found for this tenant. Please create an admin user.
            </Alert>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
}
