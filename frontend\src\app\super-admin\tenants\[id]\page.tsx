'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as CheckCircleIcon,
  Block as BlockIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { getTenantById, activateTenant, deactivateTenant, deleteTenant } from '@/services/tenantService';
import { getUsersByTenantId } from '@/services/userService';
import { TenantDto } from '@/types/tenants';
import { User } from '@/types/auth';

export default function TenantDetailsPage({ params }: { params: { id: string } }) {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [tenant, setTenant] = useState<TenantDto | null>(null);
  const [adminUsers, setAdminUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchTenantDetails = async () => {
      try {
        const tenantData = await getTenantById(params.id);
        if (tenantData) {
          setTenant(tenantData);
          
          // Fetch users for this tenant
          const users = await getUsersByTenantId(params.id);
          // Filter admin users
          const admins = users.filter(u => u.roles.includes('Admin'));
          setAdminUsers(admins);
        } else {
          setError('Tenant not found');
        }
      } catch (error) {
        console.error('Error fetching tenant details:', error);
        setError('Failed to load tenant details');
      } finally {
        setLoading(false);
      }
    };

    fetchTenantDetails();
  }, [params.id]);

  const handleStatusToggle = async () => {
    if (!tenant) return;
    
    setActionInProgress(true);
    try {
      let success;
      if (tenant.isActive) {
        success = await deactivateTenant(tenant.id);
      } else {
        success = await activateTenant(tenant.id);
      }
      
      if (success) {
        setTenant({
          ...tenant,
          isActive: !tenant.isActive
        });
      }
    } catch (error) {
      console.error('Error toggling tenant status:', error);
      setError('Failed to update tenant status');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDelete = async () => {
    if (!tenant) return;
    
    setActionInProgress(true);
    try {
      const success = await deleteTenant(tenant.id);
      
      if (success) {
        router.push('/super-admin/tenants');
      } else {
        setError('Failed to delete tenant');
      }
    } catch (error) {
      console.error('Error deleting tenant:', error);
      setError('Failed to delete tenant');
    } finally {
      setActionInProgress(false);
    }
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !tenant) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || 'Tenant not found'}
        </Alert>
        <Button
          component={Link}
          href="/super-admin/tenants"
          startIcon={<ArrowBackIcon />}
        >
          Back to Tenants
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href="/super-admin/tenants"
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Tenants
        </Button>
        <Typography variant="h4">
          Tenant Details
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <BusinessIcon sx={{ fontSize: 40, mr: 2, color: 'primary.main' }} />
                <Box>
                  <Typography variant="h5">{tenant.name}</Typography>
                  <Chip 
                    label={tenant.isActive ? 'Active' : 'Inactive'} 
                    color={tenant.isActive ? 'success' : 'default'}
                    size="small"
                    sx={{ mt: 0.5 }}
                  />
                </Box>
              </Box>
              <Box>
                <Tooltip title="Edit Tenant">
                  <IconButton 
                    component={Link} 
                    href={`/super-admin/tenants/${tenant.id}/edit`}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title={tenant.isActive ? 'Deactivate' : 'Activate'}>
                  <IconButton
                    onClick={handleStatusToggle}
                    color={tenant.isActive ? 'default' : 'success'}
                    disabled={actionInProgress}
                  >
                    {tenant.isActive ? <BlockIcon /> : <CheckCircleIcon />}
                  </IconButton>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton
                    onClick={handleDelete}
                    color="error"
                    disabled={actionInProgress}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Users
                </Typography>
                <Typography variant="body1">
                  {tenant.userCount}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body1">
                  {new Date(tenant.createdAt).toLocaleDateString()}
                </Typography>
              </Grid>
              {tenant.logoUrl && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Logo URL
                  </Typography>
                  <Typography variant="body1" sx={{ wordBreak: 'break-all' }}>
                    {tenant.logoUrl}
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Admin Users</Typography>
              <Button
                component={Link}
                href={`/super-admin/tenants/${tenant.id}/edit`}
                variant="outlined"
                size="small"
              >
                Manage Admin
              </Button>
            </Box>

            <Divider sx={{ mb: 2 }} />

            {adminUsers.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                No admin users found
              </Typography>
            ) : (
              <List>
                {adminUsers.map((admin) => (
                  <ListItem key={admin.id} divider>
                    <ListItemAvatar>
                      <Avatar>
                        <PersonIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${admin.firstName} ${admin.lastName}`}
                      secondary={
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <EmailIcon fontSize="small" sx={{ mr: 0.5, fontSize: 16 }} />
                            <Typography variant="body2">{admin.email}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <CalendarIcon fontSize="small" sx={{ mr: 0.5, fontSize: 16 }} />
                            <Typography variant="body2">
                              Created: {new Date(admin.createdAt).toLocaleDateString()}
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
