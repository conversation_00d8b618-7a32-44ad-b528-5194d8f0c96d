'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Divider,
  InputAdornment,
  IconButton,
  FormHelperText,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { createTenant } from '@/services/tenantService';
import { CreateTenantRequest } from '@/types/tenants';

export default function CreateTenantPage() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = useState<CreateTenantRequest>({
    name: '',
    logoUrl: '',
    adminFirstName: '',
    adminLastName: '',
    adminEmail: '',
    adminPassword: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    // Validate tenant name
    if (!formData.name.trim()) {
      newErrors.name = 'Tenant name is required';
    }
    
    // Validate admin first name
    if (!formData.adminFirstName.trim()) {
      newErrors.adminFirstName = 'Admin first name is required';
    }
    
    // Validate admin last name
    if (!formData.adminLastName.trim()) {
      newErrors.adminLastName = 'Admin last name is required';
    }
    
    // Validate admin email
    if (!formData.adminEmail.trim()) {
      newErrors.adminEmail = 'Admin email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.adminEmail)) {
      newErrors.adminEmail = 'Invalid email format';
    }
    
    // Validate admin password
    if (!formData.adminPassword) {
      newErrors.adminPassword = 'Admin password is required';
    } else if (formData.adminPassword.length < 8) {
      newErrors.adminPassword = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(formData.adminPassword)) {
      newErrors.adminPassword = 'Password must include uppercase, lowercase, number, and special character';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setSubmitting(true);
    setGeneralError(null);
    
    try {
      const result = await createTenant(formData);
      
      if (result) {
        setSuccess(true);
        // Reset form
        setFormData({
          name: '',
          logoUrl: '',
          adminFirstName: '',
          adminLastName: '',
          adminEmail: '',
          adminPassword: '',
        });
        
        // Redirect to tenant list after a short delay
        setTimeout(() => {
          router.push('/super-admin/tenants');
        }, 2000);
      } else {
        setGeneralError('Failed to create tenant. Please try again.');
      }
    } catch (error) {
      console.error('Error creating tenant:', error);
      setGeneralError('An unexpected error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href="/super-admin/tenants"
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Tenants
        </Button>
        <Typography variant="h4">
          Create New Tenant
        </Typography>
      </Box>
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Tenant created successfully! Redirecting...
        </Alert>
      )}
      
      {generalError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {generalError}
        </Alert>
      )}

      <Paper sx={{ p: 3 }} elevation={2}>
        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6">Tenant Information</Typography>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Tenant Name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name}
                disabled={submitting}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Logo URL (optional)"
                name="logoUrl"
                value={formData.logoUrl}
                onChange={handleChange}
                disabled={submitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6">Admin User Information</Typography>
              <FormHelperText>
                This will create the initial admin user for the tenant
              </FormHelperText>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Admin First Name"
                name="adminFirstName"
                value={formData.adminFirstName}
                onChange={handleChange}
                error={!!errors.adminFirstName}
                helperText={errors.adminFirstName}
                disabled={submitting}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Admin Last Name"
                name="adminLastName"
                value={formData.adminLastName}
                onChange={handleChange}
                error={!!errors.adminLastName}
                helperText={errors.adminLastName}
                disabled={submitting}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Admin Email"
                name="adminEmail"
                type="email"
                value={formData.adminEmail}
                onChange={handleChange}
                error={!!errors.adminEmail}
                helperText={errors.adminEmail}
                disabled={submitting}
                required
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Admin Password"
                name="adminPassword"
                type={showPassword ? 'text' : 'password'}
                value={formData.adminPassword}
                onChange={handleChange}
                error={!!errors.adminPassword}
                helperText={errors.adminPassword || 'Password must be at least 8 characters with uppercase, lowercase, number, and special character'}
                disabled={submitting}
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={togglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="button"
                  variant="outlined"
                  onClick={() => router.push('/super-admin/tenants')}
                  sx={{ mr: 2 }}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={submitting}
                  startIcon={submitting ? <CircularProgress size={20} /> : null}
                >
                  Create Tenant
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Box>
  );
}
