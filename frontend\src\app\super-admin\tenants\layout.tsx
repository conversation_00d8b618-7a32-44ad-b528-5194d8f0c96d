'use client';

import { usePathname } from 'next/navigation';
import { Box, Breadcrumbs, Typography, Link as MuiLink } from '@mui/material';
import Link from 'next/link';
import { 
  Home as HomeIcon,
  Business as BusinessIcon,
  NavigateNext as NavigateNextIcon 
} from '@mui/icons-material';

export default function TenantsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  
  // Generate breadcrumbs based on the current path
  const getBreadcrumbs = () => {
    const paths = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    
    // Home
    breadcrumbs.push({
      label: 'Home',
      href: '/',
      icon: <HomeIcon fontSize="small" sx={{ mr: 0.5 }} />
    });
    
    // Super Admin
    breadcrumbs.push({
      label: 'Super Admin',
      href: '/super-admin',
      icon: null
    });
    
    // Tenants
    breadcrumbs.push({
      label: 'Tenants',
      href: '/super-admin/tenants',
      icon: <BusinessIcon fontSize="small" sx={{ mr: 0.5 }} />
    });
    
    // Tenant ID (if present)
    if (paths.length > 2 && paths[2] !== 'create') {
      const tenantId = paths[2];
      
      breadcrumbs.push({
        label: 'Tenant Details',
        href: `/super-admin/tenants/${tenantId}`,
        icon: null
      });
      
      // Edit (if present)
      if (paths.length > 3 && paths[3] === 'edit') {
        breadcrumbs.push({
          label: 'Edit',
          href: `/super-admin/tenants/${tenantId}/edit`,
          icon: null
        });
      }
    }
    
    // Create (if present)
    if (paths.length > 2 && paths[2] === 'create') {
      breadcrumbs.push({
        label: 'Create',
        href: '/super-admin/tenants/create',
        icon: null
      });
    }
    
    return breadcrumbs;
  };
  
  const breadcrumbs = getBreadcrumbs();
  
  return (
    <Box>
      <Breadcrumbs 
        separator={<NavigateNextIcon fontSize="small" />} 
        aria-label="breadcrumb"
        sx={{ mb: 3 }}
      >
        {breadcrumbs.map((breadcrumb, index) => {
          const isLast = index === breadcrumbs.length - 1;
          
          return isLast ? (
            <Typography 
              key={breadcrumb.href} 
              color="text.primary"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {breadcrumb.icon}
              {breadcrumb.label}
            </Typography>
          ) : (
            <MuiLink
              key={breadcrumb.href}
              component={Link}
              href={breadcrumb.href}
              underline="hover"
              color="inherit"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              {breadcrumb.icon}
              {breadcrumb.label}
            </MuiLink>
          );
        })}
      </Breadcrumbs>
      
      {children}
    </Box>
  );
}
