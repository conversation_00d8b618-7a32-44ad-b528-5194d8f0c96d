'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Email as EmailIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as CheckCircleIcon,
  Block as BlockIcon,
  Business as BusinessIcon,
  VpnKey as VpnKeyIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { getUserById, activateUser, deactivateUser, deleteUser } from '@/services/userService';
import { getTenantById } from '@/services/tenantService';
import { User } from '@/types/auth';
import { TenantDto } from '@/types/tenants';

export default function UserDetailsPage({ params }: { params: { id: string } }) {
  const { user: currentUser, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [tenant, setTenant] = useState<TenantDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!currentUser?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, currentUser, router]);

  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const userData = await getUserById(params.id);
        if (userData) {
          setUser(userData);
          
          // Fetch tenant details if user has a tenant
          if (userData.tenantId) {
            const tenantData = await getTenantById(userData.tenantId);
            if (tenantData) {
              setTenant(tenantData);
            }
          }
        } else {
          setError('User not found');
        }
      } catch (error) {
        console.error('Error fetching user details:', error);
        setError('Failed to load user details');
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [params.id]);

  const handleStatusToggle = async () => {
    if (!user) return;
    
    // Prevent toggling SuperAdmin status
    if (user.roles.includes('SuperAdmin')) {
      setActionError('Cannot change status of SuperAdmin users');
      return;
    }
    
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      let success;
      if (user.isActive) {
        success = await deactivateUser(user.id);
      } else {
        success = await activateUser(user.id);
      }
      
      if (success) {
        setUser({
          ...user,
          isActive: !user.isActive
        });
        setActionSuccess(`User ${user.isActive ? 'deactivated' : 'activated'} successfully`);
        
        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      } else {
        setActionError(`Failed to ${user.isActive ? 'deactivate' : 'activate'} user`);
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      setActionError('An unexpected error occurred');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDeleteClick = () => {
    // Prevent deleting SuperAdmin
    if (user?.roles.includes('SuperAdmin')) {
      setActionError('Cannot delete SuperAdmin users');
      return;
    }
    
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!user) return;
    
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      const success = await deleteUser(user.id);
      
      if (success) {
        setDeleteDialogOpen(false);
        
        // Redirect to users list after a short delay
        setTimeout(() => {
          router.push('/super-admin/users');
        }, 1500);
        
        setActionSuccess('User deleted successfully. Redirecting...');
      } else {
        setActionError('Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      setActionError('An unexpected error occurred');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
  };

  if (isLoading || !isAuthenticated || !currentUser?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !user) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || 'User not found'}
        </Alert>
        <Button
          component={Link}
          href="/super-admin/users"
          startIcon={<ArrowBackIcon />}
        >
          Back to Users
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          component={Link}
          href="/super-admin/users"
          startIcon={<ArrowBackIcon />}
          sx={{ mr: 2 }}
        >
          Back to Users
        </Button>
        <Typography variant="h4">
          User Details
        </Typography>
      </Box>

      {actionSuccess && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {actionSuccess}
        </Alert>
      )}

      {actionError && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {actionError}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ width: 64, height: 64, mr: 2, bgcolor: 'primary.main' }}>
                  <PersonIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Box>
                  <Typography variant="h5">{user.fullName}</Typography>
                  <Chip 
                    label={user.isActive ? 'Active' : 'Inactive'} 
                    color={user.isActive ? 'success' : 'default'}
                    size="small"
                    sx={{ mt: 0.5 }}
                  />
                </Box>
              </Box>
              <Box>
                <Tooltip title="Edit User">
                  <IconButton 
                    component={Link} 
                    href={`/super-admin/users/${user.id}/edit`}
                    color="primary"
                  >
                    <EditIcon />
                  </IconButton>
                </Tooltip>
                <Tooltip title={user.isActive ? 'Deactivate' : 'Activate'}>
                  <span>
                    <IconButton
                      onClick={handleStatusToggle}
                      color={user.isActive ? 'default' : 'success'}
                      disabled={actionInProgress || user.roles.includes('SuperAdmin')}
                    >
                      {user.isActive ? <BlockIcon /> : <CheckCircleIcon />}
                    </IconButton>
                  </span>
                </Tooltip>
                <Tooltip title="Delete">
                  <span>
                    <IconButton
                      onClick={handleDeleteClick}
                      color="error"
                      disabled={actionInProgress || user.roles.includes('SuperAdmin')}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </span>
                </Tooltip>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle2" color="text.secondary">
                    Email
                  </Typography>
                </Box>
                <Typography variant="body1">{user.email}</Typography>
              </Grid>
              
              <Grid item xs={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle2" color="text.secondary">
                    Created
                  </Typography>
                </Box>
                <Typography variant="body1">
                  {new Date(user.createdAt).toLocaleDateString()}
                </Typography>
              </Grid>
              
              <Grid item xs={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle2" color="text.secondary">
                    Last Updated
                  </Typography>
                </Box>
                <Typography variant="body1">
                  {user.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }} elevation={2}>
            <Typography variant="h6" gutterBottom>
              Roles
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ mb: 2 }}>
              {user.roles.map((role) => (
                <Chip
                  key={role}
                  icon={<VpnKeyIcon />}
                  label={role}
                  color={role === 'SuperAdmin' ? 'secondary' : 'primary'}
                  variant={role === 'SuperAdmin' ? 'filled' : 'outlined'}
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>
          </Paper>

          {tenant && (
            <Paper sx={{ p: 3 }} elevation={2}>
              <Typography variant="h6" gutterBottom>
                Tenant Information
              </Typography>
              <Divider sx={{ mb: 2 }} />
              
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BusinessIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="body1">
                  <Link href={`/super-admin/tenants/${tenant.id}`} style={{ textDecoration: 'none', color: 'inherit' }}>
                    <strong>{tenant.name}</strong>
                  </Link>
                </Typography>
                <Chip 
                  label={tenant.isActive ? 'Active' : 'Inactive'} 
                  color={tenant.isActive ? 'success' : 'default'}
                  size="small"
                  sx={{ ml: 1 }}
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Created: {new Date(tenant.createdAt).toLocaleDateString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Users: {tenant.userCount}
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the user "{user.fullName}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : null}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
