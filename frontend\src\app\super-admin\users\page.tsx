'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Tooltip,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Grid,
  Divider,
  FormControlLabel,
  Switch,
  SelectChangeEvent,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
  Save as SaveIcon,
} from '@mui/icons-material';
import { 
  getAllUsers, 
  getUserById,
  updateUser, 
  activateUser, 
  deactivateUser, 
  deleteUser 
} from '@/services/userService';
import { getAllTenants } from '@/services/tenantService';
import { User } from '@/types/auth';
import { TenantDto } from '@/types/tenants';

export default function UsersPage() {
  const { user: currentUser, isLoading, isAuthenticated } = useAuth();
  const router = useRouter();
  
  // Users state
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Tenants state
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [tenantsLoading, setTenantsLoading] = useState(true);
  
  // Filters state
  const [selectedTenant, setSelectedTenant] = useState<string>('all');
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [showActiveOnly, setShowActiveOnly] = useState<boolean>(false);
  const [showFilters, setShowFilters] = useState<boolean>(false);
  
  // Action states
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [actionInProgress, setActionInProgress] = useState(false);
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const [actionError, setActionError] = useState<string | null>(null);
  
  // Edit form state
  const [editFormData, setEditFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    isActive: true,
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!currentUser?.roles.includes('SuperAdmin')) {
        router.push('/');
      }
    }
  }, [isLoading, isAuthenticated, currentUser, router]);

  // Fetch users and tenants
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usersData, tenantsData] = await Promise.all([
          getAllUsers(),
          getAllTenants()
        ]);
        
        setUsers(usersData);
        setFilteredUsers(usersData);
        setTenants(tenantsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
        setTenantsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Apply filters when filter values change
  useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedTenant, selectedRole, showActiveOnly, users]);

  const applyFilters = () => {
    let filtered = [...users];
    
    // Apply search filter
    if (searchTerm.trim() !== '') {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(user => 
        user.firstName.toLowerCase().includes(searchLower) ||
        user.lastName.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.fullName.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply tenant filter
    if (selectedTenant !== 'all') {
      filtered = filtered.filter(user => user.tenantId === selectedTenant);
    }
    
    // Apply role filter
    if (selectedRole !== 'all') {
      filtered = filtered.filter(user => user.roles.includes(selectedRole));
    }
    
    // Apply active filter
    if (showActiveOnly) {
      filtered = filtered.filter(user => user.isActive);
    }
    
    setFilteredUsers(filtered);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleTenantFilterChange = (event: SelectChangeEvent) => {
    setSelectedTenant(event.target.value);
  };

  const handleRoleFilterChange = (event: SelectChangeEvent) => {
    setSelectedRole(event.target.value);
  };

  const handleActiveFilterChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowActiveOnly(event.target.checked);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedTenant('all');
    setSelectedRole('all');
    setShowActiveOnly(false);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // User actions
  const handleEditClick = (user: User) => {
    setSelectedUser(user);
    setEditFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      isActive: user.isActive,
    });
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  const handleStatusToggle = async (user: User) => {
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      let success;
      if (user.isActive) {
        success = await deactivateUser(user.id);
      } else {
        success = await activateUser(user.id);
      }
      
      if (success) {
        // Update the user in the list
        const updatedUsers = users.map(u => 
          u.id === user.id ? { ...u, isActive: !u.isActive } : u
        );
        setUsers(updatedUsers);
        setActionSuccess(`User ${user.isActive ? 'deactivated' : 'activated'} successfully`);
        
        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      } else {
        setActionError(`Failed to ${user.isActive ? 'deactivate' : 'activate'} user`);
      }
    } catch (error) {
      console.error(`Error toggling user status:`, error);
      setActionError('An unexpected error occurred');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;
    
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      const success = await deleteUser(selectedUser.id);
      
      if (success) {
        // Remove the user from the list
        const updatedUsers = users.filter(u => u.id !== selectedUser.id);
        setUsers(updatedUsers);
        setDeleteDialogOpen(false);
        setSelectedUser(null);
        setActionSuccess('User deleted successfully');
        
        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      } else {
        setActionError('Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      setActionError('An unexpected error occurred');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedUser(null);
  };

  // Edit form handlers
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: name === 'isActive' ? checked : value
    }));
    
    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateEditForm = () => {
    const errors: Record<string, string> = {};
    
    if (!editFormData.firstName.trim()) {
      errors.firstName = 'First name is required';
    }
    
    if (!editFormData.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }
    
    if (!editFormData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(editFormData.email)) {
      errors.email = 'Invalid email format';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEditSubmit = async () => {
    if (!selectedUser || !validateEditForm()) {
      return;
    }
    
    setActionInProgress(true);
    setActionSuccess(null);
    setActionError(null);
    
    try {
      const result = await updateUser(selectedUser.id, editFormData);
      
      if (result) {
        // Update the user in the list
        const updatedUsers = users.map(u => 
          u.id === selectedUser.id ? { 
            ...u, 
            firstName: result.firstName, 
            lastName: result.lastName, 
            email: result.email,
            fullName: `${result.firstName} ${result.lastName}`,
            isActive: result.isActive 
          } : u
        );
        
        setUsers(updatedUsers);
        setEditDialogOpen(false);
        setSelectedUser(null);
        setActionSuccess('User updated successfully');
        
        // Clear success message after a delay
        setTimeout(() => {
          setActionSuccess(null);
        }, 3000);
      } else {
        setActionError('Failed to update user');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      setActionError('An unexpected error occurred');
    } finally {
      setActionInProgress(false);
    }
  };

  const handleEditCancel = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
    setFormErrors({});
  };

  // Get unique roles from all users
  const getAllRoles = () => {
    const rolesSet = new Set<string>();
    users.forEach(user => {
      user.roles.forEach(role => {
        rolesSet.add(role);
      });
    });
    return Array.from(rolesSet);
  };

  const getTenantName = (tenantId: string | undefined) => {
    if (!tenantId) return 'N/A';
    const tenant = tenants.find(t => t.id === tenantId);
    return tenant ? tenant.name : 'Unknown';
  };

  if (isLoading || !isAuthenticated || !currentUser?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          User Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<FilterListIcon />}
          onClick={toggleFilters}
          color="primary"
        >
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </Button>
      </Box>

      {actionSuccess && (
        <Box sx={{ mb: 2 }}>
          <Chip 
            label={actionSuccess} 
            color="success" 
            onDelete={() => setActionSuccess(null)}
          />
        </Box>
      )}

      {actionError && (
        <Box sx={{ mb: 2 }}>
          <Chip 
            label={actionError} 
            color="error" 
            onDelete={() => setActionError(null)}
          />
        </Box>
      )}

      <Paper sx={{ p: 2, mb: 3 }} elevation={2}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton onClick={() => setSearchTerm('')} size="small">
                      <ClearIcon />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid>

          {showFilters && (
            <>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel id="tenant-filter-label">Tenant</InputLabel>
                  <Select
                    labelId="tenant-filter-label"
                    value={selectedTenant}
                    label="Tenant"
                    onChange={handleTenantFilterChange}
                  >
                    <MenuItem value="all">All Tenants</MenuItem>
                    {tenants.map((tenant) => (
                      <MenuItem key={tenant.id} value={tenant.id}>
                        {tenant.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel id="role-filter-label">Role</InputLabel>
                  <Select
                    labelId="role-filter-label"
                    value={selectedRole}
                    label="Role"
                    onChange={handleRoleFilterChange}
                  >
                    <MenuItem value="all">All Roles</MenuItem>
                    {getAllRoles().map((role) => (
                      <MenuItem key={role} value={role}>
                        {role}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={showActiveOnly}
                        onChange={handleActiveFilterChange}
                      />
                    }
                    label="Active Users Only"
                  />
                  <Button
                    variant="outlined"
                    startIcon={<ClearIcon />}
                    onClick={resetFilters}
                    sx={{ ml: 'auto' }}
                  >
                    Reset Filters
                  </Button>
                </Box>
              </Grid>
            </>
          )}
        </Grid>
      </Paper>

      <Paper sx={{ p: 2 }} elevation={2}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Tenant</TableCell>
                <TableCell>Roles</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No users found
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>{user.fullName}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{getTenantName(user.tenantId)}</TableCell>
                    <TableCell>
                      {user.roles.map((role) => (
                        <Chip
                          key={role}
                          label={role}
                          size="small"
                          color={role === 'SuperAdmin' ? 'secondary' : 'primary'}
                          variant={role === 'SuperAdmin' ? 'filled' : 'outlined'}
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={user.isActive ? 'Active' : 'Inactive'} 
                        color={user.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(user.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit">
                        <IconButton 
                          onClick={() => handleEditClick(user)}
                          color="primary"
                          disabled={actionInProgress}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={user.isActive ? 'Deactivate' : 'Activate'}>
                        <IconButton
                          onClick={() => handleStatusToggle(user)}
                          color={user.isActive ? 'default' : 'success'}
                          disabled={actionInProgress || user.roles.includes('SuperAdmin')}
                        >
                          {user.isActive ? <BlockIcon /> : <CheckCircleIcon />}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton
                          onClick={() => handleDeleteClick(user)}
                          color="error"
                          disabled={actionInProgress || user.roles.includes('SuperAdmin')}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Edit User Dialog */}
      <Dialog
        open={editDialogOpen}
        onClose={handleEditCancel}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Edit User</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  name="firstName"
                  value={editFormData.firstName}
                  onChange={handleEditFormChange}
                  error={!!formErrors.firstName}
                  helperText={formErrors.firstName}
                  disabled={actionInProgress}
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  name="lastName"
                  value={editFormData.lastName}
                  onChange={handleEditFormChange}
                  error={!!formErrors.lastName}
                  helperText={formErrors.lastName}
                  disabled={actionInProgress}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={editFormData.email}
                  onChange={handleEditFormChange}
                  error={!!formErrors.email}
                  helperText={formErrors.email}
                  disabled={actionInProgress}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={editFormData.isActive}
                      onChange={handleEditFormChange}
                      name="isActive"
                      disabled={actionInProgress || (selectedUser?.roles.includes('SuperAdmin') ?? false)}
                    />
                  }
                  label="Active"
                />
                {selectedUser?.roles.includes('SuperAdmin') && (
                  <Typography variant="caption" color="text.secondary">
                    SuperAdmin status cannot be changed
                  </Typography>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditCancel} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button 
            onClick={handleEditSubmit} 
            color="primary" 
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : <SaveIcon />}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the user "{selectedUser?.fullName}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={actionInProgress}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={actionInProgress}
            startIcon={actionInProgress ? <CircularProgress size={20} /> : null}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
