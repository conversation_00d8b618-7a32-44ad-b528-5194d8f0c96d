'use client';

import { forwardRef } from 'react';
import Link, { LinkProps } from 'next/link';
import { useNavigation } from '@/contexts/NavigationContext';
import { usePathname } from 'next/navigation';

interface NavigationLinkProps extends LinkProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void;
}

const NavigationLink = forwardRef<HTMLAnchorElement, NavigationLinkProps>(
  ({ href, children, onClick, ...props }, ref) => {
    const { setNavigating } = useNavigation();
    const pathname = usePathname();

    const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
      // Only show loading if navigating to a different page
      const targetPath = typeof href === 'string' ? href : href.pathname || '';
      if (targetPath !== pathname) {
        setNavigating(true);
      }
      
      if (onClick) {
        onClick(e);
      }
    };

    return (
      <Link ref={ref} href={href} onClick={handleClick} {...props}>
        {children}
      </Link>
    );
  }
);

NavigationLink.displayName = 'NavigationLink';

export default NavigationLink;
