'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Box, CircularProgress, Backdrop } from '@mui/material';

export default function NavigationLoader() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const handleStart = () => {
      setLoading(true);
    };

    const handleComplete = () => {
      setLoading(false);
    };

    // Listen for route changes
    const originalPush = router.push;
    const originalReplace = router.replace;

    router.push = (...args) => {
      handleStart();
      return originalPush.apply(router, args);
    };

    router.replace = (...args) => {
      handleStart();
      return originalReplace.apply(router, args);
    };

    // Handle completion when pathname changes
    handleComplete();

    return () => {
      router.push = originalPush;
      router.replace = originalReplace;
    };
  }, [pathname, router]);

  if (!loading) return null;

  return (
    <Backdrop
      sx={{
        color: '#fbbf24',
        zIndex: (theme) => theme.zIndex.drawer + 1,
        bgcolor: 'rgba(0, 0, 0, 0.7)',
      }}
      open={loading}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <CircularProgress 
          color="inherit" 
          size={60}
          thickness={4}
        />
      </Box>
    </Backdrop>
  );
}
