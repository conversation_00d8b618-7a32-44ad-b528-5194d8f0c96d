'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { User, LoginRequest, RegisterRequest, GoogleAuthRequest, AuthResult } from '@/types/auth';
import { login, register, refreshToken, logout, googleLogin } from '@/services/authService';
import { storeToken, getToken, clearAllTokens, getTokenStorageLocation } from '@/utils/tokenStorage';

// Function to check if token is expired
const isTokenExpired = (token: string): boolean => {
  try {
    const payload = decodeToken(token);
    const exp = payload.exp * 1000; // Convert to milliseconds
    return Date.now() >= exp;
  } catch (e) {
    return true; // If there's any error parsing the token, consider it expired
  }
};

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (data: LoginRequest) => Promise<AuthResult>;
  register: (data: RegisterRequest) => Promise<AuthResult>;
  googleLogin: (data: GoogleAuthRequest) => Promise<AuthResult>;
  logout: () => Promise<void>;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function decodeToken(token: string) {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const payload = JSON.parse(window.atob(base64));
  return payload;
}

// Extract roles from JWT token payload
function extractRolesFromPayload(payload: any): string[] {
  // Check for roles in the standard claim
  const roleKey = 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role';

  if (payload[roleKey]) {
    // The role claim can be either a string (single role) or an array of strings (multiple roles)
    return Array.isArray(payload[roleKey]) ? payload[roleKey] : [payload[roleKey]];
  }

  // Fallback to check for roles in a custom claim if the standard one is not present
  if (payload.roles && Array.isArray(payload.roles)) {
    return payload.roles;
  }

  return [];
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const checkAuth = async () => {
      const token = getToken('token');
      const refreshTokenValue = getToken('refreshToken');

      if (!token || !refreshTokenValue) {
        setIsLoading(false);
        return;
      }

      // Only refresh if token is expired
      if (isTokenExpired(token)) {
        try {
          const result = await refreshToken({ refreshToken: refreshTokenValue });

          if (result.succeeded) {
            // Save the new tokens
            storeToken('token', result.accessToken!);
            storeToken('refreshToken', result.refreshToken!);

            const payload = decodeToken(result.accessToken!);
            const roles = extractRolesFromPayload(payload);

            const user: User = {
              id: payload.sub,
              email: payload.email,
              fullName: payload.name,
              firstName: '',
              lastName: '',
              roles: roles,
              tenantId: '',
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };


            // Set the user
            setUser(user);
          } else {
            // Clear tokens if refresh failed
            clearAllTokens();
          }
        } catch (error) {
          console.error('Auth check error:', error);
          clearAllTokens();
        }
      } else {
        // Token exists and is not expired, decode it and set the user
        try {
          const payload = decodeToken(token);
          const roles = extractRolesFromPayload(payload);

          const user: User = {
            id: payload.sub,
            email: payload.email,
            fullName: payload.name,
            firstName: '',
            lastName: '',
            roles: roles,
            tenantId: '',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          setUser(user);
        } catch (error) {
          console.error('Error parsing token:', error);
          clearAllTokens();
        }
      }

      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const handleLogin = async (data: LoginRequest): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await login(data);

      if (result.succeeded) {
        console.log('Login successful, setting cookies:', {
          hasAccessToken: !!result.accessToken,
          hasRefreshToken: !!result.refreshToken,
          accessTokenLength: result.accessToken?.length,
          refreshTokenLength: result.refreshToken?.length,
          accessTokenPreview: result.accessToken?.substring(0, 50) + '...',
          refreshTokenPreview: result.refreshToken?.substring(0, 20) + '...'
        });

        // Check if accessToken is valid before setting
        if (!result.accessToken) {
          console.error('Access token is missing from login response!');
          setError('Access token missing from server response');
          setIsLoading(false);
          return result;
        }

        if (!result.refreshToken) {
          console.error('Refresh token is missing from login response!');
          setError('Refresh token missing from server response');
          setIsLoading(false);
          return result;
        }

        // Check token sizes (browsers typically limit cookies to 4KB)
        const accessTokenSize = new Blob([result.accessToken]).size;
        const refreshTokenSize = new Blob([result.refreshToken]).size;

        console.log('Token sizes:', {
          accessTokenSize,
          refreshTokenSize,
          accessTokenTooLarge: accessTokenSize > 4000,
          refreshTokenTooLarge: refreshTokenSize > 4000
        });

        if (accessTokenSize > 4000) {
          console.warn('Access token is very large and might not fit in a cookie');
        }

        try {
          storeToken('token', result.accessToken);
          storeToken('refreshToken', result.refreshToken);

          console.log('Tokens stored successfully');
        } catch (error) {
          console.error('Error storing tokens:', error);
        }

        // Verify tokens were stored
        const storedToken = getToken('token');
        const storedRefreshToken = getToken('refreshToken');

        console.log('Tokens after storing:', {
          tokenStored: !!storedToken,
          refreshTokenStored: !!storedRefreshToken,
          tokenLength: storedToken?.length,
          refreshTokenLength: storedRefreshToken?.length,
          tokenStorageLocation: getTokenStorageLocation('token'),
          refreshTokenStorageLocation: getTokenStorageLocation('refreshToken')
        });

        // Decode token to get claims including roles
        const payload = decodeToken(result.accessToken!);
        const roles = extractRolesFromPayload(payload);

        console.log('User roles from token:', roles);

        // Create user object from response data
        const user: User = {
          id: result.userId!,
          email: result.email!,
          fullName: result.fullName!,
          firstName: '', // These fields are not in the response
          lastName: '',  // These fields are not in the response
          roles: roles,
          tenantId: '', // Not in the response
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setUser(user);
      } else {
        setError(result.errors?.join(', ') || 'Login failed');
      }

      setIsLoading(false);
      return result;
    } catch (error) {
      setError('An unexpected error occurred');
      setIsLoading(false);
      throw error;
    }
  };

  const handleRegister = async (data: RegisterRequest): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await register(data);

      if (result.succeeded) {
        storeToken('token', result.accessToken!);
        storeToken('refreshToken', result.refreshToken!);

        // Decode token to get claims including roles
        const payload = decodeToken(result.accessToken!);
        const roles = extractRolesFromPayload(payload);

        // Create user object from response data
        const user: User = {
          id: result.userId!,
          email: result.email!,
          fullName: result.fullName!,
          firstName: '', // These fields are not in the response
          lastName: '',  // These fields are not in the response
          roles: roles,
          tenantId: '', // Not in the response
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setUser(user);
      } else {
        setError(result.errors?.join(', ') || 'Registration failed');
      }

      setIsLoading(false);
      return result;
    } catch (error) {
      setError('An unexpected error occurred');
      setIsLoading(false);
      throw error;
    }
  };

  const handleGoogleLogin = async (data: GoogleAuthRequest): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await googleLogin(data);

      if (result.succeeded) {
        storeToken('token', result.accessToken!);
        storeToken('refreshToken', result.refreshToken!);

        // Decode token to get claims including roles
        const payload = decodeToken(result.accessToken!);
        const roles = extractRolesFromPayload(payload);

        // Create user object from response data
        const user: User = {
          id: result.userId!,
          email: result.email!,
          fullName: result.fullName!,
          firstName: '', // These fields are not in the response
          lastName: '',  // These fields are not in the response
          roles: roles,
          tenantId: '', // Not in the response
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        setUser(user);
      } else {
        setError(result.errors?.join(', ') || 'Google login failed');
      }

      setIsLoading(false);
      return result;
    } catch (error) {
      setError('An unexpected error occurred');
      setIsLoading(false);
      throw error;
    }
  };

  const handleLogout = async (): Promise<void> => {
    setIsLoading(true);

    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clean up tokens from both cookies and localStorage
      clearAllTokens();

      setUser(null);
      setIsLoading(false);
      router.push('/');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        login: handleLogin,
        register: handleRegister,
        googleLogin: handleGoogleLogin,
        logout: handleLogout,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
}
