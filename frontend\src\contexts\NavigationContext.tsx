'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Box, CircularProgress, Backdrop, Typography } from '@mui/material';

interface NavigationContextType {
  isNavigating: boolean;
  setNavigating: (loading: boolean) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

interface NavigationProviderProps {
  children: React.ReactNode;
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const [isNavigating, setIsNavigating] = useState(false);
  const pathname = usePathname();

  const setNavigating = (loading: boolean) => {
    setIsNavigating(loading);
  };

  // Auto-hide loading when pathname changes (navigation complete)
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsNavigating(false);
    }, 100); // Small delay to ensure smooth transition

    return () => clearTimeout(timer);
  }, [pathname]);

  return (
    <NavigationContext.Provider value={{ isNavigating, setNavigating }}>
      {children}
      
      {/* Navigation Loading Overlay */}
      <Backdrop
        sx={{
          color: '#fbbf24',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          bgcolor: 'rgba(74, 74, 74, 0.8)',
          backdropFilter: 'blur(4px)',
        }}
        open={isNavigating}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <CircularProgress 
            color="inherit" 
            size={60}
            thickness={4}
          />
          <Typography 
            variant="body1" 
            sx={{ 
              color: '#fbbf24',
              fontWeight: 500,
              textAlign: 'center'
            }}
          >
            Loading...
          </Typography>
        </Box>
      </Backdrop>
    </NavigationContext.Provider>
  );
}
