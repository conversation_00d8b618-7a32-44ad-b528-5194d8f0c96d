import axios from 'axios';
import { getToken, storeToken, clearAllTokens } from '@/utils/tokenStorage';

const baseURL = process.env.NEXT_PUBLIC_API_URL || '/api';

export const apiClient = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Get token using our utility function
    const token = getToken('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is due to an expired token (401) and we haven't tried to refresh yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = getToken('refreshToken');

        if (!refreshToken) {
          // No refresh token, clean up and redirect to home page
          clearAllTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/';
          }
          return Promise.reject(error);
        }

        // Try to refresh the token
        const response = await axios.post(`${baseURL}/auth/refresh-token`, {
          refreshToken,
        });

        if (response.data.succeeded) {
          // Save the new tokens using our utility function
          storeToken('token', response.data.accessToken);
          storeToken('refreshToken', response.data.refreshToken);

          // Update the Authorization header
          originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`;

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, clean up all tokens and redirect to home page
          clearAllTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/';
          }
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Something went wrong with the refresh, clean up all tokens and redirect to home page
        clearAllTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);
