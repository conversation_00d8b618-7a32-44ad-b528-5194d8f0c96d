import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // Get the token from cookies or headers
  // Note: Middleware runs on the server side and cannot access localStorage
  // We'll check if token is stored in localStorage via a cookie flag
  let token = request.cookies.get('token')?.value || request.headers.get('Authorization')?.split(' ')[1];

  // If no token in cookie but there's a storage flag indicating it's in localStorage,
  // we'll let the client-side AuthContext handle the authentication check
  const tokenStorageFlag = request.cookies.get('token_storage')?.value;
  const hasTokenInLocalStorage = tokenStorageFlag === 'localStorage';

  // Debug logging for SuperAdmin paths
  if (request.nextUrl.pathname.startsWith('/super-admin') || request.nextUrl.pathname === '/login') {
    console.log('Middleware debug:', {
      path: request.nextUrl.pathname,
      hasToken: !!token,
      tokenLength: token?.length,
      hasTokenInLocalStorage,
      tokenStorageFlag,
      cookies: Object.fromEntries(request.cookies.getAll().map(c => [c.name, c.value?.length || 0]))
    });
  }

  // Define protected paths that require authentication
  const protectedPaths = ['/dashboard', '/super-admin'];
  const isProtectedPath = protectedPaths.some(path => request.nextUrl.pathname.startsWith(path));

  // Public paths that should redirect to dashboard if already authenticated
  const publicPaths = ['/login', '/register'];
  const isPublicPath = publicPaths.some(path => request.nextUrl.pathname === path);

  // If trying to access protected route without token, redirect to login
  // But allow if token is stored in localStorage (client-side will handle auth check)
  if (isProtectedPath && !token && !hasTokenInLocalStorage) {
    const url = new URL('/login', request.url);
    url.searchParams.set('redirect', request.nextUrl.pathname);
    return NextResponse.redirect(url);
  }

  // If trying to access login/register while authenticated, redirect based on role
  if (isPublicPath && (token || hasTokenInLocalStorage)) {
    if (token) {
      // Try to decode the token to check if user is SuperAdmin
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const roles = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || [];

        // Check if user is SuperAdmin
        if (Array.isArray(roles) && roles.includes('SuperAdmin')) {
          return NextResponse.redirect(new URL('/super-admin/dashboard', request.url));
        } else {
          return NextResponse.redirect(new URL('/dashboard', request.url));
        }
      } catch (error) {
        // If token can't be decoded, redirect to dashboard as fallback
        return NextResponse.redirect(new URL('/dashboard', request.url));
      }
    } else if (hasTokenInLocalStorage) {
      // Token is in localStorage, we can't decode it server-side
      // Redirect to dashboard and let client-side routing handle the role-based redirect
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // Prevent SuperAdmin users from accessing regular dashboard
  if (request.nextUrl.pathname === '/dashboard' || request.nextUrl.pathname.startsWith('/dashboard/')) {
    try {
      if (token) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const roles = payload['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'] || [];

        // If user is SuperAdmin, redirect to SuperAdmin dashboard
        if (Array.isArray(roles) && roles.includes('SuperAdmin')) {
          return NextResponse.redirect(new URL('/super-admin/dashboard', request.url));
        }
      }
    } catch (error) {
      // If token can't be decoded, continue
    }
  }

  return NextResponse.next();
}

// Configure which routes to run middleware on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}