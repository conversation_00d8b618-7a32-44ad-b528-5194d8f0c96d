import { LoginRequest, RegisterRequest, RefreshTokenRequest, GoogleAuthRequest, AuthResult } from '@/types/auth';
import { apiClient } from '@/lib/apiClient';

export async function login(data: LoginRequest): Promise<AuthResult> {
  try {
    const response = await apiClient.post<AuthResult>('/auth/login', data);
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data as AuthResult;
    }
    return {
      succeeded: false,
      errors: ['An unexpected error occurred']
    };
  }
}

export async function register(data: RegisterRequest): Promise<AuthResult> {
  try {
    const response = await apiClient.post<AuthResult>('/auth/register', data);
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data as AuthResult;
    }
    return {
      succeeded: false,
      errors: ['An unexpected error occurred']
    };
  }
}

export async function refreshToken(data: RefreshTokenRequest): Promise<AuthResult> {
  try {
    const response = await apiClient.post<AuthResult>('/auth/refresh-token', data);
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data as AuthResult;
    }
    return {
      succeeded: false,
      errors: ['An unexpected error occurred']
    };
  }
}

export async function googleLogin(data: GoogleAuthRequest): Promise<AuthResult> {
  try {
    const response = await apiClient.post<AuthResult>('/auth/google-login', data);
    return response.data;
  } catch (error: any) {
    if (error.response && error.response.data) {
      return error.response.data as AuthResult;
    }
    return {
      succeeded: false,
      errors: ['An unexpected error occurred']
    };
  }
}

export async function logout(): Promise<void> {
  try {
    await apiClient.post('/auth/logout');
  } catch (error) {
    console.error('Logout error:', error);
  }
}
