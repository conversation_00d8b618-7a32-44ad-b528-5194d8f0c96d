import { apiClient } from '@/lib/apiClient';

export interface PermissionDto {
  id: string;
  name: string;
  description?: string;
  category: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CreatePermissionRequest {
  name: string;
  description?: string;
  category: string;
}

export interface UpdatePermissionRequest {
  name: string;
  description?: string;
  category: string;
}

export async function getAllPermissions(): Promise<PermissionDto[]> {
  try {
    const response = await apiClient.get<PermissionDto[]>('/permissions');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching permissions:', error);
    return [];
  }
}

export async function getPermissionById(id: string): Promise<PermissionDto | null> {
  try {
    const response = await apiClient.get<PermissionDto>(`/permissions/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching permission ${id}:`, error);
    return null;
  }
}

export async function getPermissionsByCategory(category: string): Promise<PermissionDto[]> {
  try {
    const response = await apiClient.get<PermissionDto[]>(`/permissions/category/${category}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching permissions for category ${category}:`, error);
    return [];
  }
}

export async function getPermissionsByRoleId(roleId: string): Promise<PermissionDto[]> {
  try {
    const response = await apiClient.get<PermissionDto[]>(`/permissions/role/${roleId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching permissions for role ${roleId}:`, error);
    return [];
  }
}

export async function createPermission(data: CreatePermissionRequest): Promise<PermissionDto | null> {
  try {
    const response = await apiClient.post<PermissionDto>('/permissions', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating permission:', error);
    throw error;
  }
}

export async function updatePermission(id: string, data: UpdatePermissionRequest): Promise<PermissionDto | null> {
  try {
    const response = await apiClient.put<PermissionDto>(`/permissions/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating permission ${id}:`, error);
    throw error;
  }
}

export async function deletePermission(id: string): Promise<boolean> {
  try {
    await apiClient.delete(`/permissions/${id}`);
    return true;
  } catch (error: any) {
    console.error(`Error deleting permission ${id}:`, error);
    throw error;
  }
}

// Helper function to validate permission name (no spaces or special characters)
export function isValidPermissionName(name: string): boolean {
  const regex = /^[a-zA-Z0-9_]+$/;
  return regex.test(name);
}

// Helper function to validate category name (only text/alphabetical characters)
export function isValidCategoryName(name: string): boolean {
  const regex = /^[a-zA-Z]+$/;
  return regex.test(name);
}

// Helper function to get all unique categories from permissions
export function getUniqueCategories(permissions: PermissionDto[]): string[] {
  const categories = new Set<string>();
  permissions.forEach(permission => {
    categories.add(permission.category);
  });
  return Array.from(categories).sort();
}

// Helper function to format permission name for display
export function formatPermissionName(name: string): string {
  // Convert camelCase or snake_case to Title Case with spaces
  return name
    .replace(/([A-Z])/g, ' $1') // Insert space before capital letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .trim()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}
