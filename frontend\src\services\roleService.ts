import { apiClient } from '@/lib/apiClient';
import { PermissionDto } from './permissionService';

export interface RoleDto {
  id: string;
  name: string;
  description?: string;
  tenantId?: string;
  tenantName?: string;
  permissions: string[];
  createdAt: string;
  updatedAt?: string;
}

export interface CreateRoleRequest {
  name: string;
  description?: string;
  tenantId?: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
}

export async function getAllRoles(): Promise<RoleDto[]> {
  try {
    const response = await apiClient.get<RoleDto[]>('/roles');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching roles:', error);
    return [];
  }
}

export async function getRoleById(id: string): Promise<RoleDto | null> {
  try {
    const response = await apiClient.get<RoleDto>(`/roles/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching role ${id}:`, error);
    return null;
  }
}

export async function getRolesByTenantId(tenantId: string): Promise<RoleDto[]> {
  try {
    const response = await apiClient.get<RoleDto[]>(`/roles/tenant/${tenantId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching roles for tenant ${tenantId}:`, error);
    return [];
  }
}

export async function createRole(data: CreateRoleRequest): Promise<RoleDto | null> {
  try {
    const response = await apiClient.post<RoleDto>('/roles', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating role:', error);
    throw error;
  }
}

export async function updateRole(id: string, data: UpdateRoleRequest): Promise<RoleDto | null> {
  try {
    const response = await apiClient.put<RoleDto>(`/roles/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating role ${id}:`, error);
    throw error;
  }
}

export async function deleteRole(id: string): Promise<boolean> {
  try {
    await apiClient.delete(`/roles/${id}`);
    return true;
  } catch (error: any) {
    console.error(`Error deleting role ${id}:`, error);
    throw error;
  }
}

export async function assignRoleToUser(roleId: string, userId: string): Promise<boolean> {
  try {
    await apiClient.post(`/roles/${roleId}/users/${userId}`);
    return true;
  } catch (error: any) {
    console.error(`Error assigning role ${roleId} to user ${userId}:`, error);
    throw error;
  }
}

export async function removeRoleFromUser(roleId: string, userId: string): Promise<boolean> {
  try {
    await apiClient.delete(`/roles/${roleId}/users/${userId}`);
    return true;
  } catch (error: any) {
    console.error(`Error removing role ${roleId} from user ${userId}:`, error);
    throw error;
  }
}

export async function assignPermissionToRole(roleId: string, permissionId: string): Promise<boolean> {
  try {
    await apiClient.post(`/roles/${roleId}/permissions/${permissionId}`);
    return true;
  } catch (error: any) {
    console.error(`Error assigning permission ${permissionId} to role ${roleId}:`, error);
    throw error;
  }
}

export async function removePermissionFromRole(roleId: string, permissionId: string): Promise<boolean> {
  try {
    await apiClient.delete(`/roles/${roleId}/permissions/${permissionId}`);
    return true;
  } catch (error: any) {
    console.error(`Error removing permission ${permissionId} from role ${roleId}:`, error);
    throw error;
  }
}

// Helper function to get role name by ID
export function getRoleNameById(roles: RoleDto[], roleId: string): string {
  const role = roles.find(r => r.id === roleId);
  return role ? role.name : 'Unknown Role';
}

// Helper function to check if a role has a specific permission
export function hasPermission(role: RoleDto, permissionName: string): boolean {
  return role.permissions.includes(permissionName);
}

// Helper function to get all permissions for a set of roles
export function getAllPermissionsForRoles(roles: RoleDto[]): string[] {
  const permissionsSet = new Set<string>();
  roles.forEach(role => {
    role.permissions.forEach(permission => {
      permissionsSet.add(permission);
    });
  });
  return Array.from(permissionsSet);
}

// Helper function to get system roles (not tenant-specific)
export function getSystemRoles(roles: RoleDto[]): RoleDto[] {
  return roles.filter(role => !role.tenantId);
}

// Helper function to get tenant roles
export function getTenantRoles(roles: RoleDto[], tenantId: string): RoleDto[] {
  return roles.filter(role => role.tenantId === tenantId);
}
