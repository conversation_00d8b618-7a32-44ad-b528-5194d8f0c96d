import { apiClient } from '@/lib/apiClient';
import { TenantMetricsDto } from '@/types/tenants';

export interface SuperAdminDashboardStats {
  totalTenants: number;
  activeTenants: number;
  inactiveTenants: number;
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  totalRecognitions: number;
  totalPointsIssued: number;
  totalPointsRedeemed: number;
  averageEngagementRate: number;
  averageRedemptionRate: number;
  userGrowthRate: number;
  recognitionGrowthRate: number;
  topTenantsByUsers: {
    id: string;
    name: string;
    userCount: number;
  }[];
}

export async function getSuperAdminDashboardStats(): Promise<SuperAdminDashboardStats | null> {
  try {
    // In a real implementation, this would be an API call to a backend endpoint
    // For now, we'll return mock data
    
    // Simulating API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    return {
      totalTenants: 25,
      activeTenants: 22,
      inactiveTenants: 3,
      totalUsers: 1250,
      activeUsers: 1050,
      inactiveUsers: 200,
      totalRecognitions: 8750,
      totalPointsIssued: 175000,
      totalPointsRedeemed: 131250,
      averageEngagementRate: 78.5,
      averageRedemptionRate: 75.0,
      userGrowthRate: 8.3,
      recognitionGrowthRate: 12.5,
      topTenantsByUsers: [
        { id: '1', name: 'Acme Corp', userCount: 120 },
        { id: '2', name: 'Globex', userCount: 98 },
        { id: '3', name: 'Initech', userCount: 85 },
        { id: '4', name: 'Umbrella Corp', userCount: 72 },
        { id: '5', name: 'Stark Industries', userCount: 65 },
      ],
    };
  } catch (error: any) {
    console.error('Error fetching SuperAdmin dashboard stats:', error);
    return null;
  }
}

export async function getAggregateMetrics(): Promise<{
  totalRecognitions: number;
  totalPointsIssued: number;
  totalPointsRedeemed: number;
  averageEngagementRate: number;
  averageRedemptionRate: number;
  recognitionGrowth: number;
} | null> {
  try {
    // In a real implementation, this would be an API call to a backend endpoint
    // For now, we'll return mock data
    
    // Simulating API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    return {
      totalRecognitions: 8750,
      totalPointsIssued: 175000,
      totalPointsRedeemed: 131250,
      averageEngagementRate: 78.5,
      averageRedemptionRate: 75.0,
      recognitionGrowth: 12.5,
    };
  } catch (error: any) {
    console.error('Error fetching aggregate metrics:', error);
    return null;
  }
}

export async function getUserOverview(): Promise<{
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  newUsersThisMonth: number;
  userGrowthRate: number;
  topTenantsByUsers: {
    id: string;
    name: string;
    userCount: number;
  }[];
} | null> {
  try {
    // In a real implementation, this would be an API call to a backend endpoint
    // For now, we'll return mock data
    
    // Simulating API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock data
    return {
      totalUsers: 1250,
      activeUsers: 1050,
      inactiveUsers: 200,
      newUsersThisMonth: 75,
      userGrowthRate: 6.4,
      topTenantsByUsers: [
        { id: '1', name: 'Acme Corp', userCount: 120 },
        { id: '2', name: 'Globex', userCount: 98 },
        { id: '3', name: 'Initech', userCount: 85 },
        { id: '4', name: 'Umbrella Corp', userCount: 72 },
        { id: '5', name: 'Stark Industries', userCount: 65 },
      ],
    };
  } catch (error: any) {
    console.error('Error fetching user overview:', error);
    return null;
  }
}
