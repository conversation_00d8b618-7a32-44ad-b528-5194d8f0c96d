import { apiClient } from '@/lib/apiClient';
import { 
  SystemConfigDto, 
  SystemHealthDto, 
  AuditLogEntry,
  FeatureToggle
} from '@/types/tenants';

export async function getSystemConfig(): Promise<SystemConfigDto | null> {
  try {
    const response = await apiClient.get<SystemConfigDto>('/system/config');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching system config:', error);
    return null;
  }
}

export async function updateSystemConfig(config: Partial<SystemConfigDto>): Promise<SystemConfigDto | null> {
  try {
    const response = await apiClient.put<SystemConfigDto>('/system/config', config);
    return response.data;
  } catch (error: any) {
    console.error('Error updating system config:', error);
    return null;
  }
}

export async function getSystemHealth(): Promise<SystemHealthDto | null> {
  try {
    const response = await apiClient.get<SystemHealthDto>('/system/health');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching system health:', error);
    return null;
  }
}

export async function getAuditLogs(page: number = 1, pageSize: number = 20): Promise<{ logs: AuditLogEntry[], totalCount: number } | null> {
  try {
    const response = await apiClient.get<{ logs: AuditLogEntry[], totalCount: number }>(`/system/audit-logs?page=${page}&pageSize=${pageSize}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching audit logs:', error);
    return null;
  }
}

export async function getErrorLogs(page: number = 1, pageSize: number = 20): Promise<{ logs: any[], totalCount: number } | null> {
  try {
    const response = await apiClient.get<{ logs: any[], totalCount: number }>(`/system/error-logs?page=${page}&pageSize=${pageSize}`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching error logs:', error);
    return null;
  }
}

export async function updateFeatureToggle(id: string, isEnabled: boolean): Promise<FeatureToggle | null> {
  try {
    const response = await apiClient.put<FeatureToggle>(`/system/feature-toggles/${id}`, { isEnabled });
    return response.data;
  } catch (error: any) {
    console.error(`Error updating feature toggle ${id}:`, error);
    return null;
  }
}

export async function sendSystemNotification(message: string, severity: 'info' | 'warning' | 'error', tenantId?: string): Promise<boolean> {
  try {
    await apiClient.post('/system/notifications', { message, severity, tenantId });
    return true;
  } catch (error: any) {
    console.error('Error sending system notification:', error);
    return false;
  }
}
