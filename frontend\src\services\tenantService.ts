import { apiClient } from '@/lib/apiClient';
import { 
  TenantDto, 
  CreateTenantRequest, 
  UpdateTenantRequest,
  TenantMetricsDto
} from '@/types/tenants';

export async function getAllTenants(): Promise<TenantDto[]> {
  try {
    const response = await apiClient.get<TenantDto[]>('/tenants');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching tenants:', error);
    return [];
  }
}

export async function getTenantById(id: string): Promise<TenantDto | null> {
  try {
    const response = await apiClient.get<TenantDto>(`/tenants/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching tenant ${id}:`, error);
    return null;
  }
}

export async function createTenant(data: CreateTenantRequest): Promise<TenantDto | null> {
  try {
    const response = await apiClient.post<TenantDto>('/tenants', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating tenant:', error);
    return null;
  }
}

export async function updateTenant(id: string, data: UpdateTenantRequest): Promise<TenantDto | null> {
  try {
    const response = await apiClient.put<TenantDto>(`/tenants/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating tenant ${id}:`, error);
    return null;
  }
}

export async function activateTenant(id: string): Promise<boolean> {
  try {
    await apiClient.put(`/tenants/${id}/activate`);
    return true;
  } catch (error: any) {
    console.error(`Error activating tenant ${id}:`, error);
    return false;
  }
}

export async function deactivateTenant(id: string): Promise<boolean> {
  try {
    await apiClient.put(`/tenants/${id}/deactivate`);
    return true;
  } catch (error: any) {
    console.error(`Error deactivating tenant ${id}:`, error);
    return false;
  }
}

export async function deleteTenant(id: string): Promise<boolean> {
  try {
    await apiClient.delete(`/tenants/${id}`);
    return true;
  } catch (error: any) {
    console.error(`Error deleting tenant ${id}:`, error);
    return false;
  }
}

export async function getTenantMetrics(id: string): Promise<TenantMetricsDto | null> {
  try {
    const response = await apiClient.get<TenantMetricsDto>(`/tenants/${id}/metrics`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching tenant metrics for ${id}:`, error);
    return null;
  }
}

export async function impersonateAdmin(tenantId: string, userId: string): Promise<{ token: string } | null> {
  try {
    const response = await apiClient.post<{ token: string }>('/auth/impersonate', { tenantId, userId });
    return response.data;
  } catch (error: any) {
    console.error('Error impersonating admin:', error);
    return null;
  }
}
