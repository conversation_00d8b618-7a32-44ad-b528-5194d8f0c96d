import { apiClient } from '@/lib/apiClient';
import { User } from '@/types/auth';

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  roleIds: string[];
  tenantId: string;
}

export interface UpdateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  isActive: boolean;
}

export interface AssignRoleRequest {
  userId: string;
  roleId: string;
}

export async function getAllUsers(): Promise<User[]> {
  try {
    const response = await apiClient.get<User[]>('/users');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching users:', error);
    return [];
  }
}

export async function getUserById(id: string): Promise<User | null> {
  try {
    const response = await apiClient.get<User>(`/users/${id}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching user ${id}:`, error);
    return null;
  }
}

export async function getUsersByTenantId(tenantId: string): Promise<User[]> {
  try {
    const response = await apiClient.get<User[]>(`/users/tenant/${tenantId}`);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching users for tenant ${tenantId}:`, error);
    return [];
  }
}

export async function createUser(data: CreateUserRequest): Promise<User | null> {
  try {
    const response = await apiClient.post<User>('/users', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating user:', error);
    return null;
  }
}

export async function updateUser(id: string, data: UpdateUserRequest): Promise<User | null> {
  try {
    const response = await apiClient.put<User>(`/users/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating user ${id}:`, error);
    return null;
  }
}

export async function activateUser(id: string): Promise<boolean> {
  try {
    await apiClient.put(`/users/${id}/activate`);
    return true;
  } catch (error: any) {
    console.error(`Error activating user ${id}:`, error);
    return false;
  }
}

export async function deactivateUser(id: string): Promise<boolean> {
  try {
    await apiClient.put(`/users/${id}/deactivate`);
    return true;
  } catch (error: any) {
    console.error(`Error deactivating user ${id}:`, error);
    return false;
  }
}

export async function deleteUser(id: string): Promise<boolean> {
  try {
    await apiClient.delete(`/users/${id}`);
    return true;
  } catch (error: any) {
    console.error(`Error deleting user ${id}:`, error);
    return false;
  }
}

export async function assignRole(userId: string, roleId: string): Promise<boolean> {
  try {
    await apiClient.post('/users/assign-role', { userId, roleId });
    return true;
  } catch (error: any) {
    console.error(`Error assigning role to user ${userId}:`, error);
    return false;
  }
}

export async function removeRole(userId: string, roleId: string): Promise<boolean> {
  try {
    await apiClient.post('/users/remove-role', { userId, roleId });
    return true;
  } catch (error: any) {
    console.error(`Error removing role from user ${userId}:`, error);
    return false;
  }
}
