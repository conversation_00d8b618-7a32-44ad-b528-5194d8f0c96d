export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  roles: string[];
  tenantId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface AuthResult {
  succeeded: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
  userId?: string;
  email?: string;
  fullName?: string;
  roles?: string[];
  permissions?: string[];
  errors?: string[];
}

export interface GoogleAuthRequest {
  idToken?: string;
  code?: string;
  redirectUri?: string;
}
