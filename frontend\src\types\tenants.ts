export interface TenantDto {
  id: string;
  name: string;
  logoUrl?: string;
  isActive: boolean;
  isDeleted: boolean;
  userCount: number;
  createdAt: string;
  updatedAt?: string;
}

export interface CreateTenantRequest {
  name: string;
  logoUrl?: string;
  adminFirstName: string;
  adminLastName: string;
  adminEmail: string;
  adminPassword: string;
}

export interface UpdateTenantRequest {
  name: string;
  logoUrl?: string;
  isActive: boolean;
}

export interface TenantMetricsDto {
  activeUsers: number;
  totalUsers: number;
  monthlyRecognitions: number;
  pointsIssued: number;
  pointsRedeemed: number;
  redemptionRate: number;
  engagementRate: number;
}

export interface SystemConfigDto {
  defaultPointsPerRecognition: number;
  maxPointsPerMonth: number;
  enableGoogleAuth: boolean;
  enableMicrosoftAuth: boolean;
  maintenanceMode: boolean;
  featureToggles: FeatureToggle[];
}

export interface FeatureToggle {
  id: string;
  name: string;
  description: string;
  isEnabled: boolean;
}

export interface SystemHealthDto {
  status: 'Healthy' | 'Degraded' | 'Unhealthy';
  components: SystemComponentHealth[];
  lastUpdated: string;
}

export interface SystemComponentHealth {
  name: string;
  status: 'Healthy' | 'Degraded' | 'Unhealthy';
  message?: string;
  lastChecked: string;
}

export interface AuditLogEntry {
  id: string;
  userId: string;
  userName: string;
  action: string;
  entityType: string;
  entityId: string;
  details: string;
  ipAddress: string;
  timestamp: string;
}
