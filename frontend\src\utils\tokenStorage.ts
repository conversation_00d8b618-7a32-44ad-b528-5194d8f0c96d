import Cookies from 'js-cookie';

// Utility function for setting cookies with consistent options
const setCookieWithOptions = (name: string, value: string) => {
  const isProduction = process.env.NODE_ENV === 'production';
  const cookieOptions = {
    secure: isProduction,
    sameSite: 'strict' as const,
    path: '/'
  };
  Cookies.set(name, value, cookieOptions);
};

// Utility function for storing tokens (uses localStorage for large tokens, cookies for small ones)
export const storeToken = (name: string, value: string) => {
  
  const tokenSize = new Blob([value]).size;
  const maxCookieSize = 4000; // Conservative limit for cookies
  
  if (tokenSize > maxCookieSize) {
    console.log(`Token ${name} is too large for cookie (${tokenSize} bytes), storing in localStorage`);
    localStorage.setItem(name, value);
    // Set a flag in cookie to indicate token is in localStorage
    setCookieWithOptions(`${name}_storage`, 'localStorage');
  } else {
    console.log(`Token ${name} fits in cookie (${tokenSize} bytes), storing in cookie`);
    setCookieWithOptions(name, value);
    // Remove localStorage version if it exists
    localStorage.removeItem(name);
    // Remove storage flag
    Cookies.remove(`${name}_storage`);
  }
};

// Utility function for retrieving tokens (checks both localStorage and cookies)
export const getToken = (name: string): string | undefined => {  
  // Check if token is stored in localStorage
  const storageFlag = Cookies.get(`${name}_storage`);
  if (storageFlag === 'localStorage') {
    return localStorage.getItem(name) || undefined;
  }
  
  // Otherwise get from cookie
  return Cookies.get(name);
};

// Utility function for clearing all tokens
export const clearAllTokens = () => {
  // Clear cookies
  Cookies.remove('token');
  Cookies.remove('refreshToken');
  Cookies.remove('token_storage');
  Cookies.remove('refreshToken_storage');
  
  // Clear localStorage (only if in browser environment)
  if (typeof window !== 'undefined') {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }
};

// Utility function to check if a token exists
export const hasToken = (name: string): boolean => {
  return !!getToken(name);
};

// Utility function to get token size
export const getTokenSize = (name: string): number => {
  const token = getToken(name);
  if (!token) return 0;
  return new Blob([token]).size;
};

// Utility function to check where a token is stored
export const getTokenStorageLocation = (name: string): 'cookie' | 'localStorage' | 'none' => {
  if (!hasToken(name)) return 'none';
  
  const storageFlag = Cookies.get(`${name}_storage`);
  return storageFlag === 'localStorage' ? 'localStorage' : 'cookie';
};
