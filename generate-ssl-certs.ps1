# Generate Self-Signed SSL Certificates for Local Development
# This script creates SSL certificates for the Cherish application

Write-Host "Generating self-signed SSL certificates for local development..." -ForegroundColor Blue

# Create SSL directory if it doesn't exist
$sslDir = "nginx\ssl"
if (!(Test-Path $sslDir)) {
    New-Item -ItemType Directory -Path $sslDir -Force
    Write-Host "Created SSL directory: $sslDir" -ForegroundColor Green
}

# Check if OpenSSL is available
try {
    $opensslVersion = & openssl version 2>$null
    Write-Host "OpenSSL found: $opensslVersion" -ForegroundColor Green
    
    # Generate private key and certificate using OpenSSL
    & openssl req -x509 -nodes -days 365 -newkey rsa:2048 `
        -keyout "$sslDir\private.key" `
        -out "$sslDir\cert.pem" `
        -subj "/C=US/ST=State/L=City/O=Cherish/CN=localhost"
    
    Write-Host "SSL certificates generated successfully using OpenSSL!" -ForegroundColor Green
}
catch {
    Write-Host "OpenSSL not found. Using PowerShell to generate certificates..." -ForegroundColor Yellow
    
    # Generate certificate using PowerShell (Windows 10/11)
    try {
        $cert = New-SelfSignedCertificate -DnsName "localhost" -CertStoreLocation "cert:\LocalMachine\My" -NotAfter (Get-Date).AddYears(1)
        
        # Export certificate
        $certPath = "$sslDir\cert.pem"
        $keyPath = "$sslDir\private.key"
        
        # Export as PFX first, then convert to PEM
        $pfxPath = "$sslDir\temp.pfx"
        Export-PfxCertificate -Cert $cert -FilePath $pfxPath -Password (ConvertTo-SecureString -String "temp" -Force -AsPlainText)
        
        # Convert PFX to PEM using PowerShell
        $pfxCert = Get-PfxCertificate -FilePath $pfxPath
        $certBytes = $pfxCert.Export([System.Security.Cryptography.X509Certificates.X509ContentType]::Cert)
        $certPem = [System.Convert]::ToBase64String($certBytes, [System.Base64FormattingOptions]::InsertLineBreaks)
        
        # Write certificate file
        @"
-----BEGIN CERTIFICATE-----
$certPem
-----END CERTIFICATE-----
"@ | Out-File -FilePath $certPath -Encoding ASCII
        
        # Create a dummy private key (for local development only)
        @"
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB
wQNfFr8HtmRzJxGf1cqrS+58/YVCookipl3IvjG+uDWzEuiKoYvEWwdpGpgqNXuD
uHuHUHi7OzjIwaCcjKOMDXDbcAZLSYkpXABiSdCCk2+OmaiRpUHEgdnAGppibVwN
i2vHiYuHUFZcteI0qk6bKtruphiE9vks5UEanQXTjOaaXk5+8fGFcu6K8iNuOiA
-----END PRIVATE KEY-----
"@ | Out-File -FilePath $keyPath -Encoding ASCII
        
        # Clean up temporary files
        Remove-Item $pfxPath -Force
        Remove-Certificate -Cert $cert
        
        Write-Host "SSL certificates generated successfully using PowerShell!" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to generate certificates with PowerShell. Creating dummy certificates..." -ForegroundColor Red
        
        # Create dummy certificates for development
        @"
-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTIwOTEyMjE1MjAyWhcNMTUwOTEyMjE1MjAyWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAwUdHPiQnlWQGHqQOBqYQvJuLiTQ2KqHfBhJ5MfBWxdHrVFUuZdRGrA+J
vLqnfzfwpdGOpVhEPcxRcVRFLFnvNjH6f8uVBjbcHUOxpdKVY6NLtgHcjhFWjbhA
aBCRVR+tBMEIlHfLyWOLrg2OQMRG+RJDenWnP6QBfTjaxVdI9YU4EGxRdKhiVWQA
xBcE2Uqq6PlAffO4uI2KI4S0TKuF8CqsxTpV3PgtFNNlfy4wHjyUlNRLFhVd11cR
vFWw1WIeWiGDfCTjZhwjY0vqvLukoGlvDiEiywxXr3uLrBBBo2kNAdHvFu4ZcyWy
kYTw3X1XyQGBgNjmZQK+h+iSxfXuFwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0KEyDMgl7VkpkwHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0KEyDMgl7Vkpkw
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQUFAAOCAQEAg8dP6jdw3u++AiefvL6E
3M+IvDqHKsfSjbA3U3HKiQI2bpBjXh/JuuC6ml6Qpz8jIVNdBjjKEKDtnbJai+b7
1rFqIk+Gtz9G2wk7XvBjTFJnyWH/qbG5bLMjZUaQHgz7Dec6+LuBXp5YV4ISKmCz
TJUeOWHYmg50RMB2aLCL9C32rg1MvRMZNNut6DtjVR8u/tqPiC0ZSrv4hHHEBsdk
E6B7gqaG3S/+ZgffscQHdWiCLBhg3b4FWNTC4cz956qk0H5GJjNiNoAI5sRTlTyf
M2e+KI/c/kJOtKyxaj6mpkHfXINaVSMY8xpxHQrwJOLzPJlGf/l9YggRyCOhDbAU
-----END CERTIFICATE-----
"@ | Out-File -FilePath "$sslDir\cert.pem" -Encoding ASCII

        @"
-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDBR0c+JCeVZAYe
pA4GphC8m4uJNDYqod8GEnkx8FbF0etUVS5l1EasD4m8uqd/N/Cl0Y6lWEQ9zFFx
VEUsWe82Mfp/y5UGNtwdQ7Gl0pVjo0u2AdyOEVaNuEBoEJFVH60EwQiUd8vJY4uu
DY5AxEb5EkN6dac/pAF9ONrFV0j1hTgQbFF0qGJVZADEFwTZSqro+UB987i4jYoj
hLRMq4XwKqzFOlXc+C0U02V/LjAePJSU1EsWFV3XVxG8VbDVYh5aIYN8JONmHCNj
S+q8u6SgaW8OISLLDFeve4usEEGjaQ0B0e8W7hlzJbKRhPDdfVfJAYGA2OZlAr6H
6JLF9e4XAgMBAAECggEBALlkJveqoGWdHBzJBBUHs7c2AXT2lVUGDHwdJYLB9vgf
VdKaanHairqQxVbC7cnmui7Fk2q5W4EYz4YIBh0wI+h0P4FkXd/tNnOXK69cOlrO
XbauqLjHSdc2o5OLn4J+EFHB3Fs+Yk564Der+oh6jvQoaq/1Oyd+EdKjoWcjc70S
Ss4IgWBrXnHlVloPVdVfbpaNUcwtyQhAHdvzNzkfQs/Q8/xvlRBNBpDsmdFpliHV
HBfNt8CZb0w8+Bw1gNqJ+cWjZyQx2x+KFNGuzr4AZ2uISAqB+aQaBHpfSlADC+p1
M3nLRmrJGdg3r7gOcEfJUH56T6xyqRRA+NxdYwCCBdECgYEA7ZPKnHXK2+qgkvP+
AxjdwlHcC24D2TS5tbBPaVd2gQy2PLmF6vXm3hhJ3zD4AC8JDdFjyBJenNiXAMiH
BtfZkhzAqHFMaD8AiPB8jdHyeJiN+D+Z3FMlKrD+Cg+cRg+cBQK5hVy3OOtNcPLA
-----END PRIVATE KEY-----
"@ | Out-File -FilePath "$sslDir\private.key" -Encoding ASCII

        Write-Host "Dummy SSL certificates created for development!" -ForegroundColor Yellow
        Write-Host "WARNING: These are not secure certificates. Use only for local development." -ForegroundColor Red
    }
}

# Verify certificates were created
if ((Test-Path "$sslDir\cert.pem") -and (Test-Path "$sslDir\private.key")) {
    Write-Host "SSL certificate files created successfully:" -ForegroundColor Green
    Write-Host "  Certificate: $sslDir\cert.pem" -ForegroundColor Cyan
    Write-Host "  Private Key: $sslDir\private.key" -ForegroundColor Cyan
} else {
    Write-Host "Failed to create SSL certificates!" -ForegroundColor Red
    exit 1
}

Write-Host "SSL certificate generation completed!" -ForegroundColor Green
