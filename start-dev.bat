@echo off
setlocal enabledelayedexpansion

echo.
echo 🚀 Starting Cherish Development Environment...
echo ================================================
echo.

REM Check if directories exist
if not exist "backend" (
    echo ❌ Backend directory not found
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Frontend directory not found
    pause
    exit /b 1
)

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET SDK is not installed or not in PATH
    echo Please install .NET 8 SDK from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed or not in PATH
    echo Please install Node.js (which includes npm) from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Install frontend dependencies if node_modules doesn't exist
if not exist "frontend\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd frontend
    call npm install
    if errorlevel 1 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
    echo ✅ Frontend dependencies installed
    echo.
)

REM Restore backend dependencies
echo 📦 Restoring backend dependencies...
cd backend
call dotnet restore
if errorlevel 1 (
    echo ❌ Failed to restore backend dependencies
    pause
    exit /b 1
)
cd ..
echo ✅ Backend dependencies restored
echo.

echo 🎯 Starting services...
echo Backend API will be available at: http://localhost:5014
echo Frontend will be available at: http://localhost:3000
echo.
echo Press Ctrl+C to stop all services
echo.

REM Start backend in a new window
echo 🔧 Starting backend...
start "Cherish Backend API" cmd /k "cd /d %~dp0backend\Cherish.Api && dotnet run"

REM Wait for backend to be ready
echo ⏳ Waiting for backend to be ready...
set /a counter=0
:wait_backend
timeout /t 2 /nobreak >nul
set /a counter+=2

REM Try to check if backend is responding (simple approach)
curl -s http://localhost:5014/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is ready!
    goto start_frontend
)

if %counter% geq 60 (
    echo ❌ Backend did not start within 60 seconds
    echo Please check the backend window for errors
    pause
    exit /b 1
)

echo ⏳ Still waiting for backend... (%counter%/60 seconds)
goto wait_backend

:start_frontend
REM Start frontend in a new window
echo 🔧 Starting frontend...
start "Cherish Frontend" cmd /k "cd /d %~dp0frontend && npm run dev"

REM Wait for frontend to be ready
echo ⏳ Waiting for frontend to be ready...
set /a frontend_counter=0
:wait_frontend
timeout /t 3 /nobreak >nul
set /a frontend_counter+=3

REM Try to check if frontend is responding
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is ready!
    goto services_ready
)

if %frontend_counter% geq 60 (
    echo ❌ Frontend did not start within 60 seconds
    echo Please check the frontend window for errors
    pause
    exit /b 1
)

echo ⏳ Still waiting for frontend... (%frontend_counter%/60 seconds)
goto wait_frontend

:services_ready
echo.
echo 🎉 All services are now running!
echo ================================================
echo.
echo 📍 Service URLs:
echo    🔗 Backend API: http://localhost:5014
echo    🔗 Swagger UI:  http://localhost:5014/swagger
echo    🔗 Health Check: http://localhost:5014/health
echo    🔗 Frontend:    http://localhost:3000
echo.
echo 💡 Tips:
echo    • You can close this window. The services will continue running in their own windows.
echo    • To stop the services, close the individual service windows or press Ctrl+C in each.
echo    • Both services will restart automatically on file changes
echo.

pause
