#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// ANSI color codes for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function checkPrerequisites() {
    log('🔍 Checking prerequisites...', colors.yellow);

    // Check if backend directory exists
    if (!fs.existsSync('backend')) {
        log('❌ Backend directory not found', colors.red);
        process.exit(1);
    }

    // Check if frontend directory exists
    if (!fs.existsSync('frontend')) {
        log('❌ Frontend directory not found', colors.red);
        process.exit(1);
    }

    log('✅ Prerequisites check passed', colors.green);
}

function installDependencies() {
    return new Promise((resolve, reject) => {
        log('📦 Installing dependencies...', colors.yellow);

        // Check if frontend node_modules exists
        if (!fs.existsSync('frontend/node_modules')) {
            log('Installing frontend dependencies...', colors.yellow);
            const npmInstall = spawn('npm', ['install'], {
                cwd: 'frontend',
                stdio: 'inherit',
                shell: true
            });

            npmInstall.on('close', (code) => {
                if (code !== 0) {
                    log('❌ Failed to install frontend dependencies', colors.red);
                    reject(new Error('Frontend dependency installation failed'));
                    return;
                }

                log('✅ Frontend dependencies installed', colors.green);

                // Restore backend dependencies
                log('Restoring backend dependencies...', colors.yellow);
                const dotnetRestore = spawn('dotnet', ['restore'], {
                    cwd: 'backend',
                    stdio: 'inherit',
                    shell: true
                });

                dotnetRestore.on('close', (code) => {
                    if (code !== 0) {
                        log('❌ Failed to restore backend dependencies', colors.red);
                        reject(new Error('Backend dependency restoration failed'));
                        return;
                    }

                    log('✅ Backend dependencies restored', colors.green);
                    resolve();
                });
            });
        } else {
            // Just restore backend dependencies
            log('Restoring backend dependencies...', colors.yellow);
            const dotnetRestore = spawn('dotnet', ['restore'], {
                cwd: 'backend',
                stdio: 'inherit',
                shell: true
            });

            dotnetRestore.on('close', (code) => {
                if (code !== 0) {
                    log('❌ Failed to restore backend dependencies', colors.red);
                    reject(new Error('Backend dependency restoration failed'));
                    return;
                }

                log('✅ Backend dependencies restored', colors.green);
                resolve();
            });
        }
    });
}

function checkBackendHealth() {
    return new Promise((resolve) => {
        const http = require('http');
        const req = http.get('http://localhost:5014/health', { timeout: 2000 }, (res) => {
            resolve(res.statusCode === 200);
        });

        req.on('error', () => resolve(false));
        req.on('timeout', () => {
            req.destroy();
            resolve(false);
        });
    });
}

function checkFrontendHealth() {
    return new Promise((resolve) => {
        const http = require('http');
        const req = http.get('http://localhost:3000', { timeout: 2000 }, (res) => {
            resolve(res.statusCode === 200);
        });

        req.on('error', () => resolve(false));
        req.on('timeout', () => {
            req.destroy();
            resolve(false);
        });
    });
}

async function waitForBackend(backend) {
    const maxWaitTime = 60; // seconds
    let waitTime = 0;

    log('⏳ Waiting for backend to be ready...', colors.yellow);

    while (waitTime < maxWaitTime) {
        // Check if backend process is still running
        if (backend.killed) {
            throw new Error('Backend process was killed');
        }

        // Check if backend is responding
        const isReady = await checkBackendHealth();
        if (isReady) {
            log('✅ Backend is ready!', colors.green);
            return;
        }

        await new Promise(resolve => setTimeout(resolve, 2000));
        waitTime += 2;
        log(`⏳ Still waiting for backend... (${waitTime}/${maxWaitTime} seconds)`, colors.yellow);
    }

    throw new Error(`Backend did not start within ${maxWaitTime} seconds`);
}

async function waitForFrontend(frontend) {
    const maxWaitTime = 60; // seconds
    let waitTime = 0;

    log('⏳ Waiting for frontend to be ready...', colors.yellow);

    while (waitTime < maxWaitTime) {
        // Check if frontend process is still running
        if (frontend.killed) {
            throw new Error('Frontend process was killed');
        }

        // Check if frontend is responding
        const isReady = await checkFrontendHealth();
        if (isReady) {
            log('✅ Frontend is ready!', colors.green);
            return;
        }

        await new Promise(resolve => setTimeout(resolve, 3000));
        waitTime += 3;
        log(`⏳ Still waiting for frontend... (${waitTime}/${maxWaitTime} seconds)`, colors.yellow);
    }

    throw new Error(`Frontend did not start within ${maxWaitTime} seconds`);
}

async function startServices() {
    log('', colors.reset);
    log('🎯 Starting services...', colors.green);
    log('Backend API will be available at: http://localhost:5014', colors.cyan);
    log('Frontend will be available at: http://localhost:3000', colors.cyan);
    log('', colors.reset);
    log('Press Ctrl+C to stop all services', colors.yellow);
    log('', colors.reset);

    // Start backend first
    log('🔧 Starting backend...', colors.yellow);
    const backend = spawn('dotnet', ['run'], {
        cwd: path.join('backend', 'Cherish.Api'),
        shell: true
    });

    // Wait for backend to be ready
    try {
        await waitForBackend(backend);
    } catch (error) {
        log(`❌ ${error.message}`, colors.red);
        backend.kill();
        process.exit(1);
    }

    // Start frontend after backend is ready
    log('🔧 Starting frontend...', colors.yellow);
    const frontend = spawn('npm', ['run', 'dev'], {
        cwd: 'frontend',
        shell: true
    });

    // Wait for frontend to be ready
    try {
        await waitForFrontend(frontend);
    } catch (error) {
        log(`❌ ${error.message}`, colors.red);
        frontend.kill();
        backend.kill();
        process.exit(1);
    }

    // Display final startup information - only after both services are confirmed ready
    log('', colors.reset);
    log('🎉 All services are now running!', colors.green);
    log('================================================', colors.green);
    log('', colors.reset);
    log('📍 Service URLs:', colors.bright);
    log('   🔗 Backend API: http://localhost:5014', colors.cyan);
    log('   🔗 Swagger UI:  http://localhost:5014/swagger', colors.cyan);
    log('   🔗 Health Check: http://localhost:5014/health', colors.cyan);
    log('   🔗 Frontend:    http://localhost:3000', colors.cyan);
    log('', colors.reset);
    log('💡 Tips:', colors.bright);
    log('   • Press Ctrl+C to stop all services', colors.yellow);
    log('   • Check the logs below for any errors', colors.yellow);
    log('   • Both services will restart automatically on file changes', colors.yellow);
    log('', colors.reset);

    // Handle backend output
    backend.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            log(`[BACKEND] ${output}`, colors.blue);
        }
    });

    backend.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            log(`[BACKEND ERROR] ${output}`, colors.red);
        }
    });

    // Handle frontend output
    frontend.stdout.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            log(`[FRONTEND] ${output}`, colors.magenta);
        }
    });

    frontend.stderr.on('data', (data) => {
        const output = data.toString().trim();
        if (output) {
            log(`[FRONTEND ERROR] ${output}`, colors.red);
        }
    });

    // Handle process exits
    backend.on('close', (code) => {
        log(`Backend process exited with code ${code}`, colors.red);
        frontend.kill();
        process.exit(code);
    });

    frontend.on('close', (code) => {
        log(`Frontend process exited with code ${code}`, colors.red);
        backend.kill();
        process.exit(code);
    });

    // Handle Ctrl+C
    process.on('SIGINT', () => {
        log('', colors.reset);
        log('🛑 Stopping services...', colors.yellow);
        backend.kill();
        frontend.kill();
        setTimeout(() => {
            log('✅ All services stopped', colors.green);
            process.exit(0);
        }, 1000);
    });
}

async function main() {
    log('🚀 Starting Cherish Development Environment...', colors.green);
    log('================================================', colors.green);
    log('', colors.reset);

    try {
        checkPrerequisites();
        await installDependencies();
        startServices();
    } catch (error) {
        log(`❌ Error: ${error.message}`, colors.red);
        process.exit(1);
    }
}

main();
