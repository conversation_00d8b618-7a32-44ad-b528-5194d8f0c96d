#!/usr/bin/env pwsh

# Cherish Development Startup Script
# This script starts both the backend (.NET API) and frontend (Next.js) concurrently

Write-Host "🚀 Starting Cherish Development Environment..." -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check if .NET is installed
if (-not (Test-Command "dotnet")) {
    Write-Host "❌ .NET SDK is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install .NET 8 SDK from https://dotnet.microsoft.com/download" -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed
if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is installed
if (-not (Test-Command "npm")) {
    Write-Host "❌ npm is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js (which includes npm) from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Prerequisites check passed" -ForegroundColor Green

# Check if directories exist
if (-not (Test-Path "backend")) {
    Write-Host "❌ Backend directory not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path "frontend")) {
    Write-Host "❌ Frontend directory not found" -ForegroundColor Red
    exit 1
}

# Install frontend dependencies if node_modules doesn't exist
if (-not (Test-Path "frontend/node_modules")) {
    Write-Host "📦 Installing frontend dependencies..." -ForegroundColor Yellow
    Set-Location "frontend"
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to install frontend dependencies" -ForegroundColor Red
        exit 1
    }
    Set-Location ".."
    Write-Host "✅ Frontend dependencies installed" -ForegroundColor Green
}

# Restore backend dependencies
Write-Host "📦 Restoring backend dependencies..." -ForegroundColor Yellow
Set-Location "backend"
dotnet restore
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to restore backend dependencies" -ForegroundColor Red
    exit 1
}
Set-Location ".."
Write-Host "✅ Backend dependencies restored" -ForegroundColor Green

Write-Host ""
Write-Host "🎯 Starting services..." -ForegroundColor Green
Write-Host "Backend API will be available at: http://localhost:5014" -ForegroundColor Cyan
Write-Host "Frontend will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Yellow
Write-Host ""

# Function to check if backend is ready
function Test-BackendReady {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5014/health" -TimeoutSec 2 -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Function to check if frontend is ready
function Test-FrontendReady {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 2 -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Start backend first
Write-Host "🔧 Starting backend..." -ForegroundColor Yellow
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location "backend/Cherish.Api"
    dotnet run
}

# Wait for backend to be ready
Write-Host "⏳ Waiting for backend to be ready..." -ForegroundColor Yellow
$maxWaitTime = 60  # Maximum wait time in seconds
$waitTime = 0
$backendReady = $false

while ($waitTime -lt $maxWaitTime -and -not $backendReady) {
    Start-Sleep -Seconds 2
    $waitTime += 2

    # Check if backend job is still running
    if ($backendJob.State -eq "Failed" -or $backendJob.State -eq "Completed") {
        Write-Host "❌ Backend failed to start" -ForegroundColor Red
        Receive-Job $backendJob
        Remove-Job $backendJob
        exit 1
    }

    # Check if backend is responding
    $backendReady = Test-BackendReady
    if ($backendReady) {
        Write-Host "✅ Backend is ready!" -ForegroundColor Green
        break
    }

    Write-Host "⏳ Still waiting for backend... ($waitTime/$maxWaitTime seconds)" -ForegroundColor Yellow
}

if (-not $backendReady) {
    Write-Host "❌ Backend did not start within $maxWaitTime seconds" -ForegroundColor Red
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob -ErrorAction SilentlyContinue
    exit 1
}

# Now start frontend
Write-Host "🔧 Starting frontend..." -ForegroundColor Yellow
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location "frontend"
    npm run dev
}

# Wait for frontend to be ready
Write-Host "⏳ Waiting for frontend to be ready..." -ForegroundColor Yellow
$maxFrontendWaitTime = 60  # Maximum wait time in seconds
$frontendWaitTime = 0
$frontendReady = $false

while ($frontendWaitTime -lt $maxFrontendWaitTime -and -not $frontendReady) {
    Start-Sleep -Seconds 3
    $frontendWaitTime += 3

    # Check if frontend job is still running
    if ($frontendJob.State -eq "Failed" -or $frontendJob.State -eq "Completed") {
        Write-Host "❌ Frontend failed to start" -ForegroundColor Red
        Receive-Job $frontendJob
        Stop-Job $backendJob -ErrorAction SilentlyContinue
        Remove-Job $backendJob -ErrorAction SilentlyContinue
        Remove-Job $frontendJob
        exit 1
    }

    # Check if frontend is responding
    $frontendReady = Test-FrontendReady
    if ($frontendReady) {
        Write-Host "✅ Frontend is ready!" -ForegroundColor Green
        break
    }

    Write-Host "⏳ Still waiting for frontend... ($frontendWaitTime/$maxFrontendWaitTime seconds)" -ForegroundColor Yellow
}

if (-not $frontendReady) {
    Write-Host "❌ Frontend did not start within $maxFrontendWaitTime seconds" -ForegroundColor Red
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob -ErrorAction SilentlyContinue
    exit 1
}

# Display final startup information - only after both services are confirmed ready
Write-Host ""
Write-Host "🎉 All services are now running!" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Service URLs:" -ForegroundColor White
Write-Host "   🔗 Backend API: http://localhost:5014" -ForegroundColor Cyan
Write-Host "   🔗 Swagger UI:  http://localhost:5014/swagger" -ForegroundColor Cyan
Write-Host "   🔗 Health Check: http://localhost:5014/health" -ForegroundColor Cyan
Write-Host "   🔗 Frontend:    http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 Tips:" -ForegroundColor White
Write-Host "   • Press Ctrl+C to stop all services" -ForegroundColor Yellow
Write-Host "   • Check the logs below for any errors" -ForegroundColor Yellow
Write-Host "   • Both services will restart automatically on file changes" -ForegroundColor Yellow
Write-Host ""

# Monitor jobs and display output
try {
    while ($true) {
        # Check if jobs are still running
        if ($backendJob.State -eq "Failed") {
            Write-Host "❌ Backend job failed" -ForegroundColor Red
            Receive-Job $backendJob
            break
        }

        if ($frontendJob.State -eq "Failed") {
            Write-Host "❌ Frontend job failed" -ForegroundColor Red
            Receive-Job $frontendJob
            break
        }

        # Display any output from jobs
        $backendOutput = Receive-Job $backendJob -Keep
        $frontendOutput = Receive-Job $frontendJob -Keep

        if ($backendOutput) {
            Write-Host "[BACKEND] $backendOutput" -ForegroundColor Blue
        }

        if ($frontendOutput) {
            Write-Host "[FRONTEND] $frontendOutput" -ForegroundColor Magenta
        }

        Start-Sleep -Seconds 1
    }
}
catch {
    Write-Host "🛑 Stopping services..." -ForegroundColor Yellow
}
finally {
    # Clean up jobs
    Stop-Job $backendJob -ErrorAction SilentlyContinue
    Stop-Job $frontendJob -ErrorAction SilentlyContinue
    Remove-Job $backendJob -ErrorAction SilentlyContinue
    Remove-Job $frontendJob -ErrorAction SilentlyContinue
    Write-Host "✅ All services stopped" -ForegroundColor Green
}
